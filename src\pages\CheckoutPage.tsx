import { useEffect, useRef, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { ArrowLeft, Box, CirclePlus, Edit2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useCart } from "@/hooks/useCart";
import { formatAddress, formatCreditCard, formatPrice } from "@/lib/utils";
import { useOptions } from "@/hooks/useOptions";
import { ShippingMethodEnum } from "@/types/shipping";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import CardLogo, { type CardType } from "@/components/shared/CardLogo";
import AddressDialog from "@/components/profiles/AddressDialog";
import { useUserProfile } from "@/hooks/useUserProfile";
import CreditCardDialog from "@/components/profiles/CreditCardDialog";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import CustomButton from "@/components/common/CustomButton";
import { useAlertDialog } from "@/hooks/useAlertDialog";
import type { ShippingOption } from "@/contexts/CartProvider";
import PickupItemNote from "@/components/checkout/PickupItemNote";
import useRecaptchaV3 from "@/hooks/useRecaptchaV3";
import LoadingOverlay from "@/components/shared/LoadingOverlay";

const CheckoutStep = {
  Cart: 1,
  Shipping: 2,
  Payment: 3,
} as const;

type CheckoutStep = (typeof CheckoutStep)[keyof typeof CheckoutStep];

const formCheckoutSchema = z.object({
  shippingMethod: z.nativeEnum(ShippingMethodEnum),
  shippingAddress: z.string().min(1, "Shipping address is required"),
  billingAddress: z.string().min(1, "Billing address is required"),
  paymentMethod: z.string().min(1, "Payment method is required"),
});

const CheckoutPage = () => {
  const navigate = useNavigate();
  const couponInputRef = useRef<HTMLInputElement>(null);
  const [step, setStep] = useState<CheckoutStep>(CheckoutStep.Cart);
  const [isDialogAddressOpen, setIsDialogAddressOpen] = useState(false);
  const [isDialogCreditCardOpen, setIsDialogCreditCardOpen] = useState(false);
  const [addressType, setAddressType] = useState<"shipping" | "billing">(
    "shipping"
  );
  const [selectedBillingAddressId, setSelectedBillingAddressId] = useState<
    string | undefined
  >();
  const [selectedShippingAddressId, setSelectedShippingAddressId] = useState<
    string | undefined
  >();
  const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState<
    string | undefined
  >();
  const {
    cart,
    getTotalItems,
    setShippingMethod,
    setShippingAddress,
    setBillingAddress,
    setMemberCardOnFile,
    setCoupon,
    setDefaultInformation,
    placeOrder,
    loading,
    subtotal,
    tax,
    total,
    shippingCost,
    discount,
    cartData,
  } = useCart();
  const { userProfile } = useUserProfile();
  const { options } = useOptions();
  const shippingSetting = options?.webstoreOptions?.shippingSetting;
  const {
    register,
    setError,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(formCheckoutSchema),
    defaultValues: {
      shippingMethod: ShippingMethodEnum.Standard,
      shippingAddress: "",
      billingAddress: "",
      paymentMethod: "",
    },
  });
  const alert = useAlertDialog();
  const recaptcha = options?.webstoreOptions?.recaptcha;
  const { isReady, isAvailable, getRecaptchaToken } = useRecaptchaV3({
    enable: recaptcha?.captchaOnBoarding ?? false,
    recaptchaSiteKey: recaptcha?.captchaSiteKey ?? "",
  });

  useEffect(() => {
    if (!userProfile) return;

    const defaultBillingAddressId =
      userProfile.userAddress?.billingAddresses?.find(
        (addr) => addr.isDefault
      )?.webstoreBillingAddressId;

    const defaultShippingAddressId =
      userProfile.userAddress?.shippingAddresses?.find(
        (addr) => addr.isDefault
      )?.id;

    const defaultCardId = userProfile.creditCards?.find(
      (card) => card.isDefault
    )?.id;

    const defaultShippingMethod = shippingSetting?.defaultShippingMethod;

    if (defaultBillingAddressId) {
      setSelectedBillingAddressId(defaultBillingAddressId.toString());
      setError("billingAddress", {});
    }
    if (defaultShippingAddressId) {
      setSelectedShippingAddressId(defaultShippingAddressId.toString());
      setError("shippingAddress", {});
    }
    if (defaultCardId) {
      setSelectedPaymentMethodId(defaultCardId.toString());
      setError("paymentMethod", {});
    }

    setDefaultInformation({
      shippingMethod: defaultShippingMethod,
      shippingAddressId: Number(defaultShippingAddressId),
      billingAddressId: Number(defaultBillingAddressId),
      memberCardOnFileId: Number(defaultCardId),
    });
  }, [
    userProfile,
    shippingSetting?.defaultShippingMethod,
    setDefaultInformation,
    setError,
  ]);

  const handleNextStep = async () => {
    window.scrollTo(0, 0);

    if (step === CheckoutStep.Shipping) {
      if (!selectedShippingAddressId) {
        setError("shippingAddress", {
          message: "Shipping address is required",
        });
        return;
      }
      if (!selectedBillingAddressId) {
        setError("billingAddress", {
          message: "Billing address is required",
        });
        return;
      }
    }

    if (step === CheckoutStep.Payment) {
      if (!selectedPaymentMethodId) {
        setError("paymentMethod", {
          message: "Payment method is required",
        });
        return;
      }
    }

    if (step < 3) {
      setStep((step + 1) as CheckoutStep);
    } else {
      const recaptchaToken = await getRecaptchaToken("checkout");
      if (!recaptchaToken) {
        alert({
          title: "Error",
          description: "Failed to get reCAPTCHA token",
          confirmText: "OK",
        });
        return;
      }
      placeOrder(recaptchaToken).then(
        (orderDetail) => {
          if (!orderDetail.success) {
            alert({
              title: "Place Order Unsuccessful",
              description: orderDetail.message,
              confirmText: "OK",
              showCancel: false,
            });
            return;
          }
          navigate(`/orders/summary/${orderDetail.data.orderId}`, {
            replace: true,
          });
        },
        () => {
          alert({
            title: "Error",
            description: "Failed to place order",
            confirmText: "OK",
            onConfirm: () => {
              navigate("/cart", { replace: true });
            },
          });
        }
      );
    }
  };

  const handlePrevStep = () => {
    window.scrollTo(0, 0);
    if (step > 1) {
      setStep((step - 1) as CheckoutStep);
    }
  };

  const handleChangeShippingMethod = (value: ShippingOption) => {
    setShippingMethod(value);
  };

  const handleSelectPaymentMethod = (value: string) => {
    setSelectedPaymentMethodId(value);
    const selectedPaymentMethod = userProfile?.creditCards?.find(
      (card) => card.id.toString() === value
    );
    if (selectedPaymentMethod) {
      setMemberCardOnFile(selectedPaymentMethod.id);
    }
  };

  const handleSelectShippingAddress = (value: string) => {
    setSelectedShippingAddressId(value);
    const selectedShippingAddress =
      userProfile?.userAddress?.shippingAddresses?.find(
        (addr) => addr.id.toString() === value
      );
    if (selectedShippingAddress) {
      setShippingAddress(selectedShippingAddress);
      setError("shippingAddress", {});
    }
  };

  const handleSelectBillingAddress = (value: string) => {
    setSelectedBillingAddressId(value);
    const selectedBillingAddress =
      userProfile?.userAddress?.billingAddresses?.find(
        (addr) => addr.webstoreBillingAddressId.toString() === value
      );
    if (selectedBillingAddress) {
      setBillingAddress(selectedBillingAddress);
      setError("billingAddress", {});
    }
  };

  const handleApplyCoupon = () => {
    if (!couponInputRef.current) return;
    const couponCode = couponInputRef.current?.value;
    if (couponCode) {
      setCoupon(couponCode);
    }
  };

  const handleClickUpdateShippingAddress = () => {
    if (!selectedShippingAddressId) return;
    const selectedShippingAddress =
      userProfile?.userAddress?.shippingAddresses?.find(
        (addr) => addr.id.toString() === selectedShippingAddressId
      );
    if (selectedShippingAddress) {
      setAddressType("shipping");
      setIsDialogAddressOpen(true);
    }
  };

  const handleClickUpdateBillingAddress = () => {
    if (!selectedBillingAddressId) return;
    const selectedBillingAddress =
      userProfile?.userAddress?.billingAddresses?.find(
        (addr) =>
          addr.webstoreBillingAddressId.toString() === selectedBillingAddressId
      );
    if (selectedBillingAddress) {
      setAddressType("billing");
      setIsDialogAddressOpen(true);
    }
  };

  let recaptchaMessage = "";
  if (!isReady) {
    recaptchaMessage = "reCAPTCHA not ready yet";
  } else if (!isAvailable) {
    recaptchaMessage = "reCAPTCHA not available";
  }

  const recaptchaContent = (
    <p className="text-red-500 text-sm text-center mt-4">{recaptchaMessage}</p>
  );

  return (
    <>
      {loading && <LoadingOverlay />}

      {isDialogAddressOpen && (
        <AddressDialog
          addressId={
            addressType === "shipping"
              ? +selectedShippingAddressId!
              : +selectedBillingAddressId!
          }
          isOpen={isDialogAddressOpen}
          onClose={() => setIsDialogAddressOpen(false)}
          form={addressType}
          title={
            addressType === "shipping"
              ? "Edit Shipping Address"
              : "Edit Billing Address"
          }
        />
      )}
      {isDialogCreditCardOpen && (
        <CreditCardDialog
          isOpen={isDialogCreditCardOpen}
          onClose={() => setIsDialogCreditCardOpen(false)}
          setIsOpen={setIsDialogCreditCardOpen}
        />
      )}
      <div className="px-4 py-10 mx-auto max-w-7xl ">
        {/* Back Button */}
        <div className="mb-6">
          <Button variant="ghost" className="pl-0" asChild>
            <Link to="/cart">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Cart
            </Link>
          </Button>
        </div>

        {/* Checkout Header */}
        <div className="mb-10">
          <h1 className="text-3xl font-bold mb-2">Checkout</h1>
          <div className="flex items-center justify-center max-w-3xl mx-auto mt-8 mb-4">
            <div
              className={`flex-1 flex flex-col items-center ${
                step >= CheckoutStep.Cart
                  ? "text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                  step >= CheckoutStep.Cart
                    ? "border-primary bg-primary text-white"
                    : "border-muted-foreground"
                }`}
              >
                1
              </div>
              <span className="text-xs mt-1">Cart</span>
            </div>
            <div
              className={`h-0.5 w-full max-w-[100px] ${
                step >= CheckoutStep.Shipping ? "bg-primary" : "bg-muted"
              }`}
            ></div>
            <div
              className={`flex-1 flex flex-col items-center ${
                step >= CheckoutStep.Shipping
                  ? "text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                  step >= CheckoutStep.Shipping
                    ? "border-primary bg-primary text-white"
                    : "border-muted-foreground"
                }`}
              >
                2
              </div>
              <span className="text-xs mt-1">Shipping</span>
            </div>
            <div
              className={`h-0.5 w-full max-w-[100px] ${
                step >= CheckoutStep.Payment ? "bg-primary" : "bg-muted"
              }`}
            ></div>
            <div
              className={`flex-1 flex flex-col items-center ${
                step >= CheckoutStep.Payment
                  ? "text-primary"
                  : "text-muted-foreground"
              }`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                  step >= CheckoutStep.Payment
                    ? "border-primary bg-primary text-white"
                    : "border-muted-foreground"
                }`}
              >
                3
              </div>
              <span className="text-xs mt-1">Payment</span>
            </div>
          </div>
        </div>

        {/* Checkout Content */}
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Checkout Form */}
          <div className="lg:col-span-2">
            {step === CheckoutStep.Cart && (
              <div className="space-y-8">
                <div className="border rounded-lg p-6">
                  <h2 className="text-xl font-semibold mb-4">
                    Your Cart ({getTotalItems()} Items)
                  </h2>
                  <div className="divide-y">
                    {cart?.map((item) => (
                      <div
                        key={item.itemCode}
                        className="py-4 flex items-center"
                      >
                        <Link to={`/product/${item.itemCode}`}>
                          <div className="h-20 w-20 flex-shrink-0 rounded-md border bg-gray-100 overflow-hidden">
                            {item.thumbnailImage ? (
                              <img
                                alt={item.imageName}
                                src={item.thumbnailImage}
                                className="h-full w-full object-fill object-center transition-transform duration-300 group-hover:scale-105"
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full w-full">
                                <Box className="text-gray-300 group-hover:text-gray-800 transition-colors h-1/2 w-1/2 mx-auto" />
                              </div>
                            )}
                          </div>
                        </Link>
                        <div className="ml-4 flex-1">
                          <Link to={`/product/${item.itemCode}`}>
                            <h3 className="text-lg font-medium text-blue-800">
                              {item.description}
                            </h3>
                          </Link>
                          {item.modifierMessage && (
                            <div className="text-sm text-muted-foreground">
                              Modifier: {item.modifierMessage}
                            </div>
                          )}
                          {item.recipientEmail && (
                            <p className="text-sm text-muted-foreground">
                              Recipient Email: {item.recipientEmail}
                            </p>
                          )}
                          <div className="flex items-center justify-between mt-2">
                            <span className="py-1 text-sm">
                              Quantity: {item.quantity}
                            </span>
                            <span className="font-medium text-base">
                              {formatPrice((item.price ?? 0) * item.quantity)}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {step === CheckoutStep.Shipping && (
              <div className="space-y-8">
                <div className="border rounded-lg p-6">
                  <h2 className="text-xl font-semibold mb-4">
                    Shipping Address
                  </h2>
                  <form className="space-y-2">
                    <Select
                      value={selectedShippingAddressId}
                      onValueChange={handleSelectShippingAddress}
                      {...register("shippingAddress")}
                    >
                      <SelectTrigger className="w-3/4">
                        <SelectValue placeholder="Select a shipping address" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {userProfile?.userAddress?.shippingAddresses?.map(
                            (address) => (
                              <SelectItem
                                key={address.id}
                                value={address.id.toString()}
                              >
                                {formatAddress(address)}
                              </SelectItem>
                            )
                          )}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    {errors.shippingAddress && (
                      <p className="text-sm text-red-500">
                        {errors.shippingAddress.message}
                      </p>
                    )}
                    <Button
                      variant="link"
                      type="button"
                      className="text-blue-700 pl-2 !mt-0"
                      onClick={handleClickUpdateShippingAddress}
                    >
                      <Edit2 /> Update Address
                    </Button>
                  </form>
                </div>

                <div className="border rounded-lg p-6">
                  <h2 className="text-xl font-semibold mb-4">
                    Billing Address
                  </h2>
                  <form className="space-y-2">
                    <Select
                      value={selectedBillingAddressId}
                      onValueChange={handleSelectBillingAddress}
                      {...register("billingAddress")}
                    >
                      <SelectTrigger className="w-3/4">
                        <SelectValue placeholder="Select a billing address" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {userProfile?.userAddress?.billingAddresses?.map(
                            (address) => (
                              <SelectItem
                                key={address.webstoreBillingAddressId}
                                value={address.webstoreBillingAddressId.toString()}
                              >
                                {formatAddress(address)}
                              </SelectItem>
                            )
                          )}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    {errors.billingAddress && (
                      <p className="text-sm text-red-500">
                        {errors.billingAddress.message}
                      </p>
                    )}
                    <Button
                      variant="link"
                      type="button"
                      className="text-blue-700 pl-2 !mt-0"
                      onClick={handleClickUpdateBillingAddress}
                    >
                      <Edit2 /> Update Address
                    </Button>
                  </form>
                </div>

                {cartData?.shippingOptions?.length > 0 && (
                  <div className="border rounded-lg p-6">
                    <h2 className="text-xl font-semibold mb-4">
                      Shipping Method
                    </h2>
                    <div className="space-y-2">
                      {cartData?.shippingOptions?.map((option) => (
                        <label
                          key={option.name}
                          className="flex items-center border rounded-md p-4 cursor-pointer"
                        >
                          <input
                            type="radio"
                            name="shipping"
                            className="mr-2"
                            value={option.name}
                            defaultChecked={option.isDefault}
                            // checked={option.isDefault}
                            onChange={() => {
                              handleChangeShippingMethod(option);
                            }}
                          />
                          <div className="flex-1">
                            <p className="font-medium">{option.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {option.description}
                            </p>
                          </div>
                          <span className="font-medium">
                            {formatPrice(option.price)}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}

                {cartData?.pickupItemDescs && (
                  <PickupItemNote pickupItemDescs={cartData.pickupItemDescs} />
                )}
              </div>
            )}

            {step === CheckoutStep.Payment && (
              <div className="space-y-8">
                <div className="border rounded-lg p-6">
                  <h2 className="text-xl font-semibold mb-4">Payment Method</h2>
                  <div className="space-y-2">
                    <Select
                      value={selectedPaymentMethodId}
                      onValueChange={handleSelectPaymentMethod}
                      {...register("paymentMethod")}
                    >
                      <SelectTrigger className="w-3/4">
                        <SelectValue placeholder="Select a shipping address" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {userProfile?.creditCards?.map((card) => (
                            <SelectItem
                              key={card.id}
                              value={card.id.toString()}
                            >
                              <CardLogo
                                cardType={
                                  card.cardType.toLocaleLowerCase() as CardType
                                }
                              />
                              {formatCreditCard(card)}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    {errors.paymentMethod && (
                      <p className="text-sm text-red-500">
                        {errors.paymentMethod.message}
                      </p>
                    )}
                    <Button
                      variant="link"
                      type="button"
                      className=" text-blue-700 pl-2 !mt-0"
                      onClick={() => setIsDialogCreditCardOpen(true)}
                    >
                      <CirclePlus /> Add Payment
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div className="col-span-1">
            <div className="border rounded-lg p-6 sticky top-20">
              <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>{formatPrice(subtotal)}</span>
                </div>
                {discount > 0 && (
                  <div className="flex justify-between text-red-600">
                    <span>Discount</span>
                    <span>{formatPrice(discount)}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Shipping</span>
                  <span>{formatPrice(shippingCost)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax</span>
                  <span>{formatPrice(tax)}</span>
                </div>
                <div className="mt-4 flex space-x-2 py-2 items-start justify-start">
                  <div className="flex flex-col w-full gap-y-1">
                    <input
                      type="text"
                      ref={couponInputRef}
                      id="coupon-code"
                      placeholder="Enter coupon code"
                      className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                    />
                    {cartData?.couponMessage && (
                      <span className="text-sm text-red-500">
                        {cartData?.couponMessage}
                      </span>
                    )}
                  </div>
                  <Button variant="secondary" onClick={handleApplyCoupon}>
                    Apply
                  </Button>
                </div>
                <div className="border-t pt-2 mt-2">
                  <div className="flex justify-between font-semibold">
                    <span>Total</span>
                    <span>{formatPrice(total)}</span>
                  </div>
                </div>
              </div>
              <div className="mt-6">
                {(recaptcha?.captchaOnBoarding ?? false) && recaptchaContent}
                <CustomButton
                  variant="default"
                  className="w-full"
                  onClick={handleNextStep}
                  size="lg"
                >
                  {step < 3 ? "Continue" : "Place Order"}
                </CustomButton>
                {step > 1 && (
                  <Button
                    variant="outline"
                    className="w-full mt-2"
                    onClick={handlePrevStep}
                  >
                    Back
                  </Button>
                )}
              </div>
              <div className="mt-4 text-xs text-muted-foreground text-center">
                By placing this order, you agree to our Terms of Service and
                Privacy Policy.
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CheckoutPage;
