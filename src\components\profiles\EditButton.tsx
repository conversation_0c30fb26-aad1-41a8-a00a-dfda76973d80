import { EditIcon } from "lucide-react";
import { Button } from "../ui/button";
import type { editProfileMode } from "./contexts/ProfileContext";
import { useProfileTab } from "./hooks/useProfileTab";

const EditButton = ({
  mode,
  variant = "outline",
  onClickCallback = () => {},
}: {
  mode: editProfileMode;
  variant?:
    | "link"
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | null
    | undefined;
  onClickCallback?: () => void;
}) => {
  const { onEditTab } = useProfileTab();
  return (
    <Button
      variant={variant}
      onClick={() => {
        onEditTab(mode);
        onClickCallback();
      }}
    >
      <EditIcon />
      Edit
    </Button>
  );
};
export default EditButton;
