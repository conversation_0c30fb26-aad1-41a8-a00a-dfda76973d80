import { type ReactNode } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";

export type DataTableColumn<T> = {
  header: string;
  key: keyof T;
  render?: (value: T[keyof T], row: T) => ReactNode;
  colClassName?: string;
  rowClassName?: string;
};

type Props<T> = {
  data: T[];
  columns: DataTableColumn<T>[];
  pageSize?: number;
  currentPage: number; // 👈 controlled by parent
  totalFooter?: number;
  totalData?: number;
  onClickRow?: (data: T) => void;
  onNext?: () => void;
  onPrevious?: () => void;
};

const DataTable = <T extends object>({
  data,
  columns,
  currentPage,
  pageSize = 10,
  totalFooter,
  totalData,
  onClickRow = () => {},
  onNext = () => {},
  onPrevious = () => {},
}: Props<T>) => {
  const pagedData = data;
  const totalPage = Math.ceil((totalData ?? data.length) / pageSize);

  const onClickRowLocal = (
    e: React.MouseEvent<HTMLTableRowElement, MouseEvent>,
    data: T
  ) => {
    e.preventDefault();
    onClickRow(data);
  };

  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            {columns.map((col, idx) => (
              <TableHead key={`idx-${idx}`} className={col.colClassName}>
                {col.header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>

        <TableBody>
          {pagedData.map((row, rowIdx) => (
            <TableRow
              className="cursor-pointer"
              key={`rowIdx-${rowIdx}`}
              onClick={(e) => onClickRowLocal(e, row)}
            >
              {columns.map((col, colIdx) => (
                <TableCell key={`colIdx-${colIdx}`}>
                  {col.render
                    ? col.render(row[col.key], row)
                    : String(row[col.key])}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>

        {totalFooter && (
          <TableFooter>
            <TableRow>
              <TableCell colSpan={3}>Total</TableCell>
              <TableCell className="text-right">{totalFooter}</TableCell>
            </TableRow>
          </TableFooter>
        )}
      </Table>

      <div className="flex justify-between items-center mt-4 px-2">
        <span className="text-sm text-gray-600">
          Showing page {currentPage} of {totalPage}
        </span>
        <div className="space-x-2">
          <button
            onClick={onPrevious}
            className="px-2 py-1 text-sm border rounded disabled:opacity-30"
            disabled={currentPage === 1}
          >
            Prev
          </button>
          <button
            onClick={onNext}
            className="px-2 py-1 text-sm border rounded disabled:opacity-30"
            disabled={currentPage >= totalPage}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default DataTable;