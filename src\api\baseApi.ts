import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import type {
  BaseQueryFn,
  FetchArgs,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query";
import { clearAllLocalStorage, getLocalStorage } from "@/lib/utils";
import { localStorageWebsiteIdKey } from "@/constants/constant";
import type { RootState } from "@/store";

const baseQuery = fetchBaseQuery({
  baseUrl: import.meta.env.VITE_REFERENCES_API,
  prepareHeaders: (headers, { getState }) => {
    const token = (getState() as RootState).auth.token?.access_token;

    const websiteId = getLocalStorage<string>(localStorageWebsiteIdKey);
    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }
    if (!headers.has("Content-Type")) {
      headers.set("Content-Type", "application/json");
    }
    headers.set("Accept", "application/json");
    headers.set("x-apikey", import.meta.env.VITE_API_KEY);
    headers.set("client-id", import.meta.env.VITE_CLIENT_ID);
    headers.set("x-componentid", import.meta.env.VITE_X_COMPONENT_ID);
    headers.set("x-productid", import.meta.env.VITE_X_PRODUCT_ID);
    if (websiteId) {
      headers.set("x-websiteid", websiteId);
    }
    return headers;
  },
});

const baseQueryWithReauth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const result = await baseQuery(args, api, extraOptions);

  if (result.error && result.error.status === 401) {
    clearAllLocalStorage();
    window.location.href = `${import.meta.env.VITE_BASE_PATH}login`;
  }

  return result;
};

export const baseApi = createApi({
  reducerPath: "baseApi",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["Option", "Product", "Cart", "Order", "UserProfile"],
  endpoints: () => ({}),
});
