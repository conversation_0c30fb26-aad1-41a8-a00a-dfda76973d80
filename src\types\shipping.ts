export const ShippingMethodEnum = {
  Standard: 1,
  Express: 2,
  Overnight: 3,
  Pickup: 4,
} as const;

export type ShippingMethodEnumType =
  (typeof ShippingMethodEnum)[keyof typeof ShippingMethodEnum];

export const ShippingMethodMap: Record<number, ShippingMethodEnumType> = {
  1: ShippingMethodEnum.Standard,
  2: ShippingMethodEnum.Express,
  3: ShippingMethodEnum.Overnight,
  4: ShippingMethodEnum.Pickup,
};

export type ShippingMethod = {
  type: ShippingMethodEnumType;
  price: number;
};
