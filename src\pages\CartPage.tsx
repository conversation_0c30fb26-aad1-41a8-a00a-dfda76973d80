import { Link, useNavigate } from "react-router-dom";
import { ArrowLeft, Box, Trash2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { useCart } from "@/hooks/useCart";
import { debounce, formatPrice } from "@/lib/utils";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";

const CartPage = () => {
  const {
    cart,
    removeFromCart,
    increaseQuantity,
    decreaseQuantity,
    isEmpty,
    getTotalItems,
  } = useCart();
  const { subtotal, tax } = useCart();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  const handleClickRemoteItem = (itemCode: string) => {
    removeFromCart(itemCode)
      .then((response) => {
        if (response?.success) {
          toast({
            title: "✅ Item has been removed from your cart.",
            variant: "default",
          });
        } else {
          toast({
            title: "❌ Remove from cart failed",
            description: response?.message,
            variant: "destructive",
          });
        }
      })
      .catch((error) => {
        toast({
          title: "❌ Remove from cart failed",
          description: error?.data?.message ?? "Failed to remove from cart",
          variant: "destructive",
        });
      });
  };

  const handleClickProceedToCheckout = () => {
    if (isAuthenticated) {
      navigate("/checkout");
    } else {
      navigate("/login?returnUrl=/checkout");
    }
  };

  const totalExcludingShipping = subtotal + tax;

  return (
    <div className="px-4 py-10 mx-auto max-w-7xl ">
      {/* Back Button */}
      <div className="mb-6">
        <Button variant="ghost" className="pl-0" asChild>
          <Link to="/">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Link>
        </Button>
      </div>

      {/* Checkout Content */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* Checkout Form */}
        <div className="lg:col-span-2">
          <div className="space-y-8">
            <div className="border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">
                Your Cart ({getTotalItems()} Items)
              </h2>
              <div className="divide-y">
                {cart?.map((item) => (
                  <div key={item.itemCode} className="py-4 flex items-center">
                    <Link to={`/product/${item.itemCode}`}>
                      <div className="h-20 w-20 flex-shrink-0 rounded-md border bg-gray-100 overflow-hidden">
                        {item.thumbnailImage ? (
                          <img
                            alt={item.imageName}
                            src={item.thumbnailImage}
                            className="h-full w-full object-fill object-center transition-transform duration-300 group-hover:scale-105"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full w-full">
                            <Box className="text-gray-300 group-hover:text-gray-800 transition-colors h-1/2 w-1/2 mx-auto" />
                          </div>
                        )}
                      </div>
                    </Link>
                    <div className="ml-4 flex-1">
                      <Link to={`/product/${item.itemCode}`}>
                        <h3 className="text-lg font-medium text-blue-800">
                          {item.description}
                        </h3>
                      </Link>
                      {item.modifierMessage && (
                        <div className="text-sm text-muted-foreground">
                          Modifier: {item.modifierMessage}
                        </div>
                      )}
                      <div>
                        {item.recipientEmail && (
                          <p className="text-sm text-muted-foreground">
                            Recipient Email: {item.recipientEmail}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center border rounded-md">
                            <Button
                              size="sm"
                              variant="ghost"
                              type="button"
                              className="px-2 py-1 w-10"
                              onClick={() =>
                                debounce(decreaseQuantity)(item.itemCode)
                              }
                            >
                              -
                            </Button>
                            <span className="py-1 px-2">{item.quantity}</span>
                            <Button
                              size="sm"
                              variant="ghost"
                              type="button"
                              className="px-2 py-1 w-10"
                              onClick={() =>
                                debounce(increaseQuantity)(item.itemCode)
                              }
                            >
                              +
                            </Button>
                          </div>
                          <div>
                            <Button
                              variant="outline"
                              size="sm"
                              type="button"
                              onClick={() =>
                                handleClickRemoteItem(item.itemCode)
                              }
                              className="text-destructive border-destructive hover:bg-destructive/90 hover:text-destructive-foreground"
                            >
                              <Trash2 className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                        </div>
                        <span className="font-medium">
                          {formatPrice((item.price ?? 0) * item.quantity)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
                {isEmpty && (
                  <div className="py-4 text-center text-lg text-muted-foreground">
                    Your cart is currently empty.
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Order Summary */}
        <div className="col-span-1">
          <div className="border rounded-lg p-6 sticky top-20">
            <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>{formatPrice(subtotal)}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax</span>
                <span>{formatPrice(tax)}</span>
              </div>
              <div className="border-t pt-2 mt-2">
                <div className="flex justify-between font-semibold">
                  <span>Total</span>
                  <span>{formatPrice(totalExcludingShipping)}</span>
                </div>
              </div>
            </div>
            <div className="mt-6">
              <Button
                className="w-full mt-2"
                disabled={isEmpty}
                type="submit"
                onClick={handleClickProceedToCheckout}
              >
                Proceed to Checkout
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;
