import { baseApi } from "./baseApi";
import type { TokenResponse, VerifyUserResponse, WebstoreUser } from "@/types";
import type {
  ValidatePhoneNumber,
  ValidatePhoneResultViewModel,
  WebstoreChangePasswordViewModel,
} from "./models";
import { oidcConfig } from "@/auth/oidcConfig";
import {
  clearAccessToken,
  setAccessToken,
  setUserInfo,
} from "@/store/authSlice";

const userApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    verifyUser: builder.mutation<VerifyUserResponse, string>({
      query: (email: string) => ({
        url: "api/Webstores/VerifyUser",
        method: "POST",
        body: {
          email,
        },
      }),
    }),
    login: builder.mutation<
      TokenResponse,
      { username: string; password: string; recaptchaToken: string | undefined }
    >({
      query: ({ username, password, recaptchaToken }) => {
        const body = new URLSearchParams();
        body.append("grant_type", "password");
        body.append("client_id", oidcConfig.client_id);
        body.append("client_secret", oidcConfig.client_secret);
        body.append("username", username);
        body.append("password", password);
        body.append("scope", oidcConfig.scope);
        if (recaptchaToken) {
          body.append("recaptchaToken", recaptchaToken);
        }

        return {
          url: `${import.meta.env.VITE_IDENTITY_API}connect/token`,
          method: "POST",
          body,
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        };
      },
      async onQueryStarted(_, { queryFulfilled, dispatch }) {
        try {
          const { data } = await queryFulfilled;
          dispatch(setAccessToken(data));
        } catch (err) {
          console.error("Login failed", err);
        }
      },
    }),
    getUser: builder.query<WebstoreUser, void>({
      query: () => ({
        url: `${import.meta.env.VITE_IDENTITY_API}connect/userinfo`,
        method: "GET",
      }),
      async onQueryStarted(_, { queryFulfilled, dispatch }) {
        try {
          const { data } = await queryFulfilled;
          dispatch(setUserInfo(data));
        } catch (err) {
          console.error("Get user failed", err);
        }
      },
    }),
    logout: builder.mutation<void, string | undefined>({
      query: (token) => {
        if (!token) {
          return { url: "", method: "GET" };
        }

        const body = new URLSearchParams();
        body.append("access_token", token);
        body.append("client_id", import.meta.env.VITE_CLIENT_ID);
        body.append(
          "client_secret",
          import.meta.env.VITE_CLIENT_SECRET as string
        );

        return {
          url: `${import.meta.env.VITE_IDENTITY_API}myconnect/token/cancel`,
          method: "POST",
          body,
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        };
      },
      async onQueryStarted(_, { queryFulfilled, dispatch }) {
        try {
          await queryFulfilled;
          dispatch(clearAccessToken());
        } catch (err) {
          console.error("Logout failed", err);
        }
      },
    }),
    validatePhoneNumber: builder.mutation<
      ValidatePhoneResultViewModel,
      ValidatePhoneNumber
    >({
      query: (body: ValidatePhoneNumber) => {
        return {
          url: "api/Webstores/ValidatePhoneNumber",
          method: "POST",
          body,
        };
      },
    }),
    changePassword: builder.mutation<boolean, WebstoreChangePasswordViewModel>({
      query: (body: WebstoreChangePasswordViewModel) => {
        return {
          url: "api/Webstores/ChangePassword",
          method: "POST",
          body,
        };
      },
    }),
  }),
});

export const {
  useVerifyUserMutation,
  useLoginMutation,
  useLazyGetUserQuery,
  useLogoutMutation,
  useValidatePhoneNumberMutation,
  useChangePasswordMutation,
} = userApi;
