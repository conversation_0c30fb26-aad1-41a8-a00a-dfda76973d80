import { useOptions } from "@/hooks/useOptions";

const PrivacyPolicyPage = () => {
  const { options } = useOptions();

  return (
    <div className="container px-4 py-10 mx-auto min-h-[600px] max-w-7xl">
      <div
        dangerouslySetInnerHTML={{
          __html:
            options?.webstoreOptions?.policySetting?.privacyPolicyContent ?? "",
        }}
      />
    </div>
  );
};

export default PrivacyPolicyPage;
