import { useCallback, useMemo, useState } from "react";
import { MapPin } from "lucide-react";
import ImagePreviewGroupMobile from "./ImagePreviewGroupPropsMobile";
import { Link } from "react-router-dom";
import { format } from "date-fns";
import { useGetOrderHistoryQuery } from "@/api/orderApi";

const pageSize = 5;

const OrdersMobilePage = () => {
  const [currentPage, setCurrentPage] = useState(1);

  const { data, isLoading, isFetching } = useGetOrderHistoryQuery({
    currentPage,
    pageSize,
  });

  const order = useMemo(() => data?.items,[data?.items])


  const totalCount = data?.totalItems ?? 0;
  const hasMore = (order?.length ?? 0) < totalCount;

  const handleViewMore = useCallback(() => {
    if (hasMore) {
      setCurrentPage((prev) => prev + 1);
    }
  }, [hasMore]);

  if (isLoading && currentPage === 1) {
    return <div className="text-center py-10">Loading orders...</div>;
  }

  if (!order?.length) {
    return (
      <div className="flex items-center justify-center my-6">
        No order history
      </div>
    );
  }

  return (
    <div className="px-4 py-10 mx-auto max-w-7xl">
      <h2 className="text-2xl font-bold sm:text-3xl mb-4">Orders</h2>

      <div className="flex flex-col space-y-4">
        {order.map((d) => (
          <Link to={`${d.orderID}`} key={`order-${d.orderID}`}>
            <div className="border pb-4 p-4 rounded">
              <div className="flex space-x-2 items-start bg-secondary p-4 rounded mb-4">
                <MapPin />
                <div className="flex flex-col">
                  <div className="text-sm font-medium">{d.orderStatus}</div>
                  <div className="text-xs text-muted-foreground">
                    {format(new Date(d.orderDate), "MMM d, yyyy")}
                  </div>
                </div>
              </div>

              <div className="mb-4">
                <ImagePreviewGroupMobile images={d.orderItems.map(i => i.thumbnailImage)} />
              </div>

              <div className="flex flex-col">
                <div className="font-medium">{d.orderItems.length} items</div>
                <div className="text-sm text-muted-foreground">Order #{d.orderID}</div>
              </div>

              <div className="font-medium mt-4">
                ${d.total?.toFixed(2) ?? "0.00"}
              </div>
            </div>
          </Link>
        ))}
      </div>

      {hasMore && (
        <div className="text-center mt-6">
          <button
            onClick={handleViewMore}
            disabled={isFetching}
            className="text-primary underline text-sm disabled:opacity-50"
          >
            {isFetching ? "Loading..." : "View more"}
          </button>
        </div>
      )}
    </div>
  );
};

export default OrdersMobilePage;