import React, { useMemo } from "react";
import { type WebstoreOptionsResponse } from "@/api/optionApi";
import { OptionsContext } from "./OptionsContext";

const OptionsProvider = ({
  option,
  children,
}: {
  option: WebstoreOptionsResponse;
  children: React.ReactNode;
}) => {
  const contextValue = useMemo(
    () => ({
      options: option,
    }),
    [option]
  );

  return (
    <OptionsContext.Provider value={contextValue}>
      {children}
    </OptionsContext.Provider>
  );
};

export default OptionsProvider;
