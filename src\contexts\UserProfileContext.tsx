import { createContext } from "react";
import type {

  UserProfile,
} from "@/types";
import type { ApiResponse } from "@/api/productApi";
import type { BillAddressViewModel, ShippingAddressViewModel, WebstoreProfileViewModel } from "@/api/models";

export interface UserProfileContextProps {
  userProfile: UserProfile | null;
  loading: boolean;
  updateBillingAddress: (id: number, address: BillAddressViewModel) => Promise<void>;
  updateShippingAddress: (id: number, address: ShippingAddressViewModel) => Promise<void>;
  addBillingAddress: (address: BillAddressViewModel) => Promise<void>;
  addShippingAddress: (address: ShippingAddressViewModel) => Promise<void>;
  deleteBillingAddress: (id: number) => Promise<void>;
  deleteShippingAddress: (id: number) => Promise<void>;
  updateUserProfile: (profile: WebstoreProfileViewModel) => Promise<void>
  setDefaultShippingAddress: (id: number) => Promise<void>
  defaultShippingLoading: boolean
  setDefaultBillingAddress: (id: number) => Promise<void>
  defaultBillingLoading: boolean
  setDefaultCardOnFile: (id: number) => Promise<ApiResponse<void>>;
  deleteCardOnFile: (id: number) => Promise<ApiResponse<void>>;
  getUserProfile: () => Promise<void>;
}

export const UserProfileContext = createContext<
  UserProfileContextProps | undefined
>(undefined);
