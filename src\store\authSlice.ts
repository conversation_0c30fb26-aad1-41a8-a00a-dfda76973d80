import type { TokenResponse, WebstoreUser } from "@/types";
import { createSlice, type PayloadAction } from "@reduxjs/toolkit";

interface AuthState {
  token: TokenResponse | null;
  user: WebstoreUser | null;
}

const initialState: AuthState = {
  token: null,
  user: null,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setAccessToken: (state, action: PayloadAction<TokenResponse | null>) => {
      state.token = action.payload;
    },
    setUserInfo: (state, action: PayloadAction<WebstoreUser>) => {
      state.user = action.payload;
    },
    clearAccessToken: (state) => {
      state.token = null;
    },
  },
});

export const { setAccessToken, clearAccessToken, setUserInfo } =
  authSlice.actions;
export default authSlice.reducer;
