import { Link, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { z } from "zod";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import { But<PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import WelcomeBanner from "@/components/authen/WelcomeBanner";
import { useVerifyUserMutation } from "@/api/userApi";
import CustomButton from "@/components/common/CustomButton";
import { useAuth } from "@/hooks/useAuth";
import type { VerifyUserResponse } from "@/types";
import { userManager } from "@/auth/oidcConfig";
import useRecaptchaV3 from "@/hooks/useRecaptchaV3";
import { useOptions } from "@/hooks/useOptions";

const formVerificationSchema = z.object({
  email: z.string().email(),
});

const formLoginSchema = z.object({
  email: z.string().email(),
  password: z.string(),
});

const LoginPage = () => {
  const { options } = useOptions();
  const navigate = useNavigate();
  const [isVerifiedUser, setIsVerifiedUser] = useState(false);
  const [verifyUser, { isLoading: isLoadingVerification }] =
    useVerifyUserMutation();
  const { login, isLoading: isLoadingLogin, authError } = useAuth();

  const formVerification = useForm({
    resolver: zodResolver(formVerificationSchema),
    defaultValues: {
      email: "",
    },
  });

  const formLogin = useForm({
    resolver: zodResolver(formLoginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const recaptcha = options?.webstoreOptions.recaptcha;
  const { isReady, isAvailable, getRecaptchaToken } = useRecaptchaV3({
    enable: recaptcha?.captchaOnBoarding ?? false,
    recaptchaSiteKey: recaptcha?.captchaSiteKey ?? "",
  });

  const { control } = formLogin;
  const emailLogin = useWatch({ control, name: "email" });

  useEffect(() => {
    userManager.createSigninRequest();
  }, []);

  const handleSubmitVerification = (
    formData: z.infer<typeof formVerificationSchema>
  ) => {
    verifyUser(formData.email)
      .unwrap()
      .then((response: VerifyUserResponse) => {
        if (response.isFoundUser) {
          setIsVerifiedUser(true);
          formLogin.setValue("email", formData.email);
        } else {
          setIsVerifiedUser(false);
          navigate("/disallow-registration");
        }
      })
      .catch((error) => {
        console.error("Verification failed:", error);
      });
  };

  const handleSubmitLogin = async (
    formData: z.infer<typeof formLoginSchema>
  ) => {
    login({ ...formData, recaptchaToken: await getRecaptchaToken("login") });
  };

  const renderVerifiedContent = () => (
    <section className="flex flex-1 justify-center pt-24">
      <div className="flex flex-col items-center w-[399px]">
        <h2 className="mb-6 text-2xl font-bold  w-3/4">
          Please Enter Email to Continue
        </h2>
        <Form {...formVerification}>
          <form
            className="w-3/4"
            onSubmit={formVerification.handleSubmit(handleSubmitVerification)}
          >
            <div className="space-y-4">
              <div>
                <label
                  className="block text-sm font-medium mb-1"
                  htmlFor="email"
                >
                  Email
                </label>
                <input
                  autoComplete="email"
                  type="email"
                  id="email"
                  {...formVerification.register("email")}
                  className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>
            </div>
            <CustomButton
              type="submit"
              variant="default"
              isLoading={isLoadingVerification}
            >
              Next
            </CustomButton>
            <div className="flex justify-center items-center mt-4">
              <span className="text-sm text-muted-foreground">
                Don't have an account?
              </span>
              <Link to="/register">
                <Button
                  size="icon"
                  variant="link"
                  className="ml-2 text-blue-600"
                >
                  Sign up
                </Button>
              </Link>
            </div>
          </form>
        </Form>
      </div>
    </section>
  );

  const renderLoginContent = () => (
    <section className="flex flex-1 justify-center pt-24">
      <div className="flex flex-col items-center w-[399px]">
        <h2 className="mb-4 text-2xl font-bold  w-3/4">
          Please Enter Email to Continue
        </h2>
        <Form {...formLogin}>
          <form
            className="w-3/4"
            onSubmit={formLogin.handleSubmit(handleSubmitLogin)}
          >
            <div className="space-y-4">
              <div>
                <label
                  className="block text-sm font-medium mb-1"
                  htmlFor="email"
                >
                  Email
                </label>
                <input
                  readOnly
                  type="email"
                  autoComplete="email"
                  id="email"
                  {...formLogin.register("email")}
                  placeholder="<EMAIL>"
                  className="w-full border-none px-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>
              <div>
                <label
                  className="block text-sm font-medium mb-1"
                  htmlFor="password"
                >
                  Pasword
                </label>
                <input
                  autoComplete="new-password"
                  type="password"
                  id="password"
                  {...formLogin.register("password")}
                  className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>
            </div>
            {authError && (
              <p className="text-red-500 text-sm text-center mt-4">
                {authError}
              </p>
            )}
            {(recaptcha?.captchaOnBoarding ?? false) && recaptchaContent}
            <CustomButton
              type="submit"
              variant="default"
              isLoading={isLoadingLogin}
            >
              Sign In
            </CustomButton>
            <div className="flex justify-center items-center mt-4">
              <Link to={`/recovery-password/${emailLogin}`}>
                <Button
                  size="icon"
                  variant="link"
                  className="ml-2 text-blue-600"
                >
                  Password Recovery
                </Button>
              </Link>
            </div>
          </form>
        </Form>
      </div>
    </section>
  );

  let recaptchaMessage = "";
  if (!isReady) {
    recaptchaMessage = "reCAPTCHA not ready yet";
  } else if (!isAvailable) {
    recaptchaMessage = "reCAPTCHA not available";
  }

  const recaptchaContent = (
    <p className="text-red-500 text-sm text-center mt-4">{recaptchaMessage}</p>
  );

  return (
    <main className="flex flex-1 min-h-[600px] max-w-7xl mx-auto px-4">
      <aside className="flex justify-center items-center bg-gray-100 w-[600px] max-md:w-6/12 max-sm:hidden">
        <WelcomeBanner />
      </aside>
      {isVerifiedUser ? renderLoginContent() : renderVerifiedContent()}
    </main>
  );
};

export default LoginPage;
