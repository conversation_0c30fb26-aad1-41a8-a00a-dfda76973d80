import {
  createContext,
} from "react";
import { type WebstoreProduct, type WebstoreProductDetail } from "@/api/productApi";

export interface ProductsContextType {
  allProducts: WebstoreProduct[];
  loading: boolean;
  getFilteredProducts: (departmentId: string | null) => WebstoreProduct[];
  getSelectedProduct: (productId: string | null) => Promise<WebstoreProductDetail | null>;
}

export const ProductsContext = createContext<ProductsContextType | undefined>(
  undefined
);


