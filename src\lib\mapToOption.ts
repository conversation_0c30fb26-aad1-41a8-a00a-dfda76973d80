export type Option<TValue = string> = {
  value: TValue;
  label: string;
};

export const mapToOption = <
  T,
  ValueKey extends keyof T,
>(
  data: T[],
  valueKey: ValueKey,
  labelKey: keyof T | ((item: T) => string),
): Option<T[ValueKey]>[] => {
  return data.map((item) => ({
    value: item[valueKey],
    label: typeof labelKey === "function"
      ? labelKey(item)
      : String(item[labelKey]),
  }));
}