import {
  useLoginMutation,
  useLogoutMutation,
  useLazyGetUserQuery,
} from "@/api/userApi";
import { clearAllLocalStorage } from "@/lib/utils";
import type { WebstoreUser } from "@/types";
import React, { useState, useMemo, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { AuthContext } from "./AuthContext";
import { useCart } from "@/hooks/useCart";
import type { RootState } from "@/store";
import { useSelector } from "react-redux";

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const userInfo = useSelector((state: RootState) => state.auth.user);
  const token = useSelector((state: RootState) => state.auth.token);

  const [user, setUser] = useState<WebstoreUser | null>(null);
  const location = useLocation();
  const [authError, setAuthError] = useState<string | null>(null);
  const [loginMutation, { isLoading: isLoadingLogin }] = useLoginMutation();
  const [logoutMutation] = useLogoutMutation();
  const [triggerGetUser, { isLoading: isLoadingUser }] = useLazyGetUserQuery();
  const navigate = useNavigate();
  const { mergeGuestCartWithUserCart, removeCart } = useCart();

  useEffect(() => {
    setUser(userInfo);
  }, [userInfo]);

  const login = React.useCallback(
    async (data: {
      email: string;
      password: string;
      recaptchaToken?: string;
    }): Promise<void> => {
      setAuthError(null);
      try {
        await loginMutation({
          username: data.email,
          password: data.password,
          recaptchaToken: data.recaptchaToken,
        }).unwrap();

        const response = await triggerGetUser().unwrap();
        setUser(response);
        mergeGuestCartWithUserCart();

        const params = new URLSearchParams(location.search);
        const returnUrl = params.get("returnUrl");
        if (returnUrl) {
          navigate(returnUrl);
          return;
        }

        navigate("/");
      } catch (error: unknown) {
        if (error && typeof error === "object" && "data" in error) {
          const apiError = error as { data?: { error_description?: string } };
          if (apiError.data?.error_description) {
            setAuthError(apiError.data.error_description);
          }
        }
        console.error("Login failed:", error);
        throw error;
      }
    },
    [
      loginMutation,
      triggerGetUser,
      mergeGuestCartWithUserCart,
      location.search,
      navigate,
    ]
  );

  const logout = React.useCallback(() => {
    removeCart().then(() => {
      logoutMutation(token?.access_token)
        .unwrap()
        .then(() => {
          setUser(null);
          clearAllLocalStorage();
          // window.location.href = `${import.meta.env.VITE_BASE_PATH}login`;
        });
    });
  }, [logoutMutation, removeCart, token?.access_token]);

  useEffect(() => {
    if (token?.expires_in) {
      const timeoutId = setTimeout(() => {
        logout();
      }, token.expires_in * 1000);

      return () => clearTimeout(timeoutId);
    }
  }, [logout, navigate, token]);

  return (
    <AuthContext.Provider
      value={useMemo(
        () => ({
          user,
          isAuthenticated: !!userInfo,
          login,
          logout,
          isLoading: isLoadingLogin || isLoadingUser,
          authError,
        }),
        [
          user,
          userInfo,
          login,
          logout,
          isLoadingLogin,
          isLoadingUser,
          authError,
        ]
      )}
    >
      {children}
    </AuthContext.Provider>
  );
};
