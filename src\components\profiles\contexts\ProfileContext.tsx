import type { Address } from "@/types";
import { createContext } from "react";

export type editProfileMode = "personal" | "billing" | "shipping" | "none";

export type ProfileContextType = {
  editTab: editProfileMode;
  onEditTab: (tab: editProfileMode) => void;
  displayAddress: (address: Address | undefined) => string
};

export const ProfileContext = createContext<ProfileContextType | undefined>(undefined);