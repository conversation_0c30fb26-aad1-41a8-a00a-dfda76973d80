import { useProfileTab } from "../hooks/useProfileTab";
import EditButton from "../EditButton";
import { useMemo } from "react";
import { useUserProfile } from "@/hooks/useUserProfile";
import BillAddressForm from "../../shared/form/BillAddressForm";
import NoDefaultAddress from "../NoDefaultAddress";

const BillAddressTab = () => {
  const { userProfile } = useUserProfile();
  const { editTab, displayAddress, onEditTab } = useProfileTab();

  const defaultBillAddress = useMemo(
    () => userProfile?.userAddress.billingAddresses.find((i) => i.isDefault),
    [userProfile?.userAddress.billingAddresses]
  );

  return (
    <div className="border rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold">Biiling Address</h2>
        {editTab === "none" && <EditButton mode="billing" />}
      </div>

      {editTab === "billing" ? (
        <BillAddressForm
          id={defaultBillAddress?.webstoreBillingAddressId}
          onCancel={() => onEditTab("none")}
          disableSetIsDefault
        />
      ) : !defaultBillAddress ? (
        <NoDefaultAddress form="billing" />
      ) : (
        <div
          className="flex items-center justify-between mb-6"
          key={`bill-${defaultBillAddress.webstoreBillingAddressId}`}
        >
          <div>
            {displayAddress(defaultBillAddress)}

            {defaultBillAddress.isDefault && (
              <span className="ml-2 inline-block rounded-full bg-blue-500 text-white text-xs px-2 py-0.5 font-semibold">
                Default
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
export default BillAddressTab;
