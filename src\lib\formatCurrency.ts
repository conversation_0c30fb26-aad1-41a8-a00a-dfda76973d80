const localeCurrencyMap: Record<string, string> = {
  "en-US": "USD",
  "th-TH": "THB",
  "en-GB": "GBP",
  "ja-JP": "JPY",
  "de-DE": "EUR",
  "fr-FR": "EUR",
  // add more as needed
};

export function formatCurrency(value: number, locale = "th-TH"): string {
  const currency = localeCurrencyMap[locale] ?? "USD";

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
  }).format(value);
}
