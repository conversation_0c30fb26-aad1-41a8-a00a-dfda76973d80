import { useOptions } from "@/hooks/useOptions";
import { PolicyLinkType } from "@/types/enum";
import { Globe2, Mail, Map, Phone } from "lucide-react";
import { Link } from "react-router-dom";

const Footer = () => {
  const { options } = useOptions();
  const policySetting = options?.webstoreOptions?.policySetting;
  const adminCourseDetail = options?.webstoreOptions?.adminCourseDetail;

  return (
    <footer className="bg-background border-t">
      <div className="px-4 md:px-36 py-12 mx-auto">
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
          <div className="mx-auto text-center sm:text-left">
            <h3 className="text-sm font-semibold mb-4 min-w-60">INFORMATION</h3>
            <ul className="space-y-2 text-sm">
              {policySetting?.enableTermOfService && (
                <li>
                  <Link
                    to={
                      policySetting?.termOfServiceLinkType ===
                      PolicyLinkType.Hyperlink.toString()
                        ? policySetting?.termOfServiceHyperlink
                        : "/policy/term-and-condition"
                    }
                    target="_blank"
                    className="text-muted-foreground hover:text-primary"
                  >
                    Term & Conditions
                  </Link>
                </li>
              )}
              {policySetting?.enablePrivacyPolicy && (
                <li>
                  <Link
                    to={
                      policySetting?.privacyPolicyLinkType ===
                      PolicyLinkType.Hyperlink.toString()
                        ? policySetting?.privacyPolicyHyperlink
                        : "/policy/privacy"
                    }
                    target="_blank"
                    className="text-muted-foreground hover:text-primary"
                  >
                    Privacy Policy
                  </Link>
                </li>
              )}
              {policySetting?.enableRefundPolicy && (
                <li>
                  <Link
                    to={
                      policySetting?.refundPolicyLinkType ===
                      PolicyLinkType.Hyperlink.toString()
                        ? policySetting?.refundPolicyHyperlink
                        : "/policy/refund"
                    }
                    target="_blank"
                    className="text-muted-foreground hover:text-primary"
                  >
                    Refund Policy
                  </Link>
                </li>
              )}
              {policySetting?.enableCancellationPolicy && (
                <li>
                  <Link
                    to={
                      policySetting?.cancellationPolicyLinkType ===
                      PolicyLinkType.Hyperlink.toString()
                        ? policySetting?.cancellationPolicyHyperlink
                        : "/policy/cancellation"
                    }
                    target="_blank"
                    className="text-muted-foreground hover:text-primary"
                  >
                    Cancellation Policy
                  </Link>
                </li>
              )}
            </ul>
          </div>

          <div className=" mx-auto text-center sm:text-left">
            <h3 className="text-sm font-semibold mb-4 min-w-60">ABOUT US</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <div className="flex items-start justify-center sm:justify-start">
                  <Map className="h-5 w-5 mr-3" />
                  <div>
                    <h4 className="text-sm text-muted-foreground max-w-44">
                      {adminCourseDetail?.storeAddress}
                    </h4>
                  </div>
                </div>
              </li>
              <li>
                <div className="flex items-start justify-center sm:justify-start">
                  <Phone className="h-5 w-5 mr-3" />
                  <div>
                    <h4 className="text-sm text-muted-foreground">
                      {adminCourseDetail?.storePhone}
                    </h4>
                  </div>
                </div>
              </li>
              <li>
                <div className="flex items-start justify-center sm:justify-start">
                  <Mail className="h-5 w-5 mr-3" />
                  <div>
                    <a
                      href={`mailto:${adminCourseDetail?.storeEmail}`}
                      className="text-sm text-muted-foreground"
                    >
                      {adminCourseDetail?.storeEmail}
                    </a>
                  </div>
                </div>
              </li>
              {adminCourseDetail?.storeWebsite && (
                <li>
                  <div className="flex items-start justify-center sm:justify-start">
                    <Globe2 className="h-5 w-5 mr-3" />
                    <div>
                      <Link
                        target="_blank"
                        to={adminCourseDetail?.storeWebsite}
                        className="text-sm text-muted-foreground max-w-44 hover:underline"
                      >
                        {adminCourseDetail?.storeWebsite}
                      </Link>
                    </div>
                  </div>
                </li>
              )}
            </ul>
          </div>
        </div>

        <div className="pt-4 mt-8">
          <div className="flex flex-col items-center justify-center gap-4 md:flex-row">
            <p className="text-xs text-muted-foreground">
              © {new Date().getFullYear()} All Rights Reserved, Club Prophet LLC
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
