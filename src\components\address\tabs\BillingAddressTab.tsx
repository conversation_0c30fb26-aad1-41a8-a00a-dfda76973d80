import type { BillAddressViewModel } from "@/api/models";
import AddressDialog from "@/components/profiles/AddressDialog";
import ConfirmDeleteDialog from "@/components/profiles/ConfirmDeleteDialog";
import NoAddresses from "@/components/shared/NoAddresses";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { useUserProfile } from "@/hooks/useUserProfile";
import { Pencil, Plus, Trash2 } from "lucide-react";
import { useCallback, useMemo, useState } from "react";

const BillingAddressTab = () => {
  const {
    userProfile,
    deleteBillingAddress,
    setDefaultBillingAddress,
    defaultBillingLoading,
    loading,
  } = useUserProfile();

  const addresses = useMemo<BillAddressViewModel[]>(
    () => userProfile?.userAddress.billingAddresses ?? [],
    [userProfile?.userAddress.billingAddresses]
  );

  const [dialog, setDialog] = useState<"modify" | "confirmDelete" | "none">(
    "none"
  );
  const [editAddress, setEditAddress] = useState<BillAddressViewModel>();
  const [removeAddressId, setRemoveAddressId] = useState<number>();

  const handleAdd = () => setDialog("modify");

  const handleEdit = (edit: BillAddressViewModel) => {
    setEditAddress(edit);
    setDialog("modify");
  };

  const handleRemove = (removeId: number) => {
    setRemoveAddressId(removeId);
    setDialog("confirmDelete");
  };

  const closeDialog = () => setDialog("none");

  const onDeleteBill = useCallback(async () => {
    try {
      if (!removeAddressId) return;
      await deleteBillingAddress(removeAddressId);
      toast({
        title: "✅ Delete successfully.",
      });
      closeDialog();
    } catch (err) {
      console.error("Unable to delete bill Address.", err);
      toast({
        title: "❌ Unable to update bill Address",
      });
    }
  }, [deleteBillingAddress, removeAddressId]);

  const onSetAsDefault = useCallback(
    async (id: number) => {
      await setDefaultBillingAddress(id);
    },
    [setDefaultBillingAddress]
  );

  if (loading) return <>...loading</>;

  return (
    <div className="md:col-span-3">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Billing Addresses</h2>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add New Address
        </Button>
      </div>

      {addresses.length === 0 ? (
        <NoAddresses />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {addresses.map((address) => (
            <div key={address.email} className="border rounded-lg p-6 relative">
              {address.isDefault && (
                <div className="absolute top-2 right-2">
                  <span className="px-2 py-1 bg-green-600 text-white text-xs rounded-md font-medium">
                    Default
                  </span>
                </div>
              )}
              <h3 className="font-medium mb-2">
                {address.firstName} {address.lastName}
              </h3>
              <div className="text-sm text-muted-foreground space-y-1">
                <p>{address.street}</p>
                <p>
                  {address.city}, {address.state} {address.zip}
                </p>
                <p>{address.country}</p>
              </div>
              <div className="mt-4 flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEdit(address)}
                >
                  <Pencil className="h-3.5 w-3.5 mr-1" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-destructive border-destructive hover:bg-destructive/90 hover:text-destructive-foreground"
                  onClick={() => handleRemove(address.webstoreBillingAddressId)}
                >
                  <Trash2 className="h-3.5 w-3.5 mr-1" />
                  Delete
                </Button>
                {!address.isDefault && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      onSetAsDefault(address.webstoreBillingAddressId)
                    }
                    disabled={defaultBillingLoading}
                  >
                    Set as Default
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      <AddressDialog
        form="billing"
        addressId={editAddress?.webstoreBillingAddressId}
        isOpen={dialog === "modify"}
        onClose={closeDialog}
        title={editAddress ? "Add New Address" : "Edit Address"}
      />

      <ConfirmDeleteDialog
        title="Confirm Delete billing address"
        isOpen={dialog === "confirmDelete"}
        onClose={closeDialog}
        onDelete={() => onDeleteBill()}
        message={`Are you sure you want to delete? This action cannot
        be undone.`}
      />
    </div>
  );
};
export default BillingAddressTab;
