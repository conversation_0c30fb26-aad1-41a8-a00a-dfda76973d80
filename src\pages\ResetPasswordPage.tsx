import {
  useIsResetPasswordExpiredMutation,
  useResetPasswordMutation,
} from "@/api/authApi";
import { Status } from "@/api/enum/status";
import { useLoginMutation } from "@/api/userApi";
import CustomButton from "@/components/common/CustomButton";
import FieldInput from "@/components/input/FieldInput";
import LoadingOverlay from "@/components/shared/LoadingOverlay";
import useValidationSchema from "@/hooks/useValidationSchema";
import { getErrorMessage } from "@/lib/reactQueryUtils";
import { decodeBase64 } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useCallback, useEffect, useMemo } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { useNavigate, useSearchParams } from "react-router-dom";
import { z } from "zod";

const resetURL = window.location.href;
interface ResetPasswordForm {
  password: string;
  confirmPassword: string;
}

const ResetPasswordPage = () => {
  const [searchParams] = useSearchParams();
  const encodeEmail = searchParams.get("email");
  const token = searchParams.get("token");
  const decodeEmail = decodeBase64(encodeEmail ?? "")
  const navigate = useNavigate();
  const [
    isResetPasswordExpired,
    {
      data: isResetPasswordExpiredResponse,
      isLoading: isResetPasswordExpiredLoading,
    },
  ] = useIsResetPasswordExpiredMutation();

  const [
    resetPassword,
    {
      data: resetPasswordResponse,
      isLoading: isResetPasswordLoading,
      error: resetPasswordError,
    },
  ] = useResetPasswordMutation();

  const [login, { isLoading: isLoginLoading }] = useLoginMutation();

  const isLoading = useMemo<boolean>(
    () =>
      isResetPasswordExpiredLoading || isResetPasswordLoading || isLoginLoading,
    [isLoginLoading, isResetPasswordExpiredLoading, isResetPasswordLoading]
  );

  const notAvailableResetLink = useMemo<boolean>(
    () => isResetPasswordExpiredResponse?.status !== Status.Success,
    [isResetPasswordExpiredResponse?.status]
  );

  const { passwordSchema, confirmPasswordSchema } = useValidationSchema();

  const form = useForm<ResetPasswordForm>({
    mode: "onChange",
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
    resolver: zodResolver(
      z.object({
        password: passwordSchema,
        confirmPassword: confirmPasswordSchema,
      })
    ),
  });

  const { control, handleSubmit } = form;

  const onSubmit = useCallback<SubmitHandler<ResetPasswordForm>>(
    async (formData: ResetPasswordForm) => {
      try {
        
        const response = await resetPassword({
          newPassword: formData.password,
          confirmPassword: formData.confirmPassword,
          email: decodeEmail,
          url: resetURL,
          token: token ?? ""
        }).unwrap();

        if (response.status === Status.Success) {
          await login({
            username: decodeEmail,
            password: formData.password
          }).unwrap();

          navigate("/")
        }
      } catch (err) {
        console.error(err);
      }
    },
    [decodeEmail, login, navigate, resetPassword, token]
  );

  useEffect(() => {
    if (isResetPasswordExpiredResponse || !decodeEmail || !token) return;

    isResetPasswordExpired({
      email: decodeEmail,
      token: token ?? ""
    });
  }, [decodeEmail, isResetPasswordExpired, isResetPasswordExpiredResponse, token]);

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center px-4">
      {isLoading && <LoadingOverlay />}

      <div className="bg-white p-8 rounded-2xl shadow-md w-full max-w-md">
        <h2 className="text-2xl font-semibold text-gray-800 text-center">
          Reset Your Password
        </h2>

        {isResetPasswordExpiredResponse && (
          <p
            className={`text-s text-center p-3 ${
              isResetPasswordExpiredResponse.status === Status.Success
                ? "text-gray-700"
                : "text-red-600"
            }`}
          >
            {isResetPasswordExpiredResponse.message}
          </p>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
          <div>
            <FieldInput<ResetPasswordForm>
              name="password"
              control={control}
              label="New password"
              disabled={notAvailableResetLink}
            />
          </div>

          <div>
            <FieldInput<ResetPasswordForm>
              name="confirmPassword"
              control={control}
              label="Confirm password"
              disabled={notAvailableResetLink}
            />
          </div>

          {resetPasswordResponse && (
            <p
              className={`text-s text-center p-3 ${
                resetPasswordResponse.status === Status.Success
                  ? "text-gray-700"
                  : "text-red-600"
              }`}
            >
              {resetPasswordResponse.message}
            </p>
          )}

          {resetPasswordError && (
            <p className="text-s text-center p-3 text-red-600">
              {getErrorMessage(resetPasswordError)}
            </p>
          )}

          <CustomButton
            variant="default"
            type="submit"
            disabled={notAvailableResetLink}
          >
            {isLoginLoading ? "Login" : "Reset Password"}
          </CustomButton>
        </form>
      </div>
    </div>
  );
};
export default ResetPasswordPage;
