import { Loader2Icon } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { cn } from "@/lib/utils";

interface ButtonProps {
  variant: "default" | "destructive" | "outline" | "secondary" | "ghost";
  isLoading?: boolean;
  type?: "button" | "submit" | "reset";
  children: React.ReactNode;
  disabled?: boolean
  onClick?: () => void;
  size?: "default" | "sm" | "lg" | "icon";
}

const CustomButton = ({
  isLoading,
  variant,
  type, disabled,
  children,
  className,
  onClick,
  size,
}: ButtonProps & { className?: string }) => {
  return (
    <Button
      className={cn("mt-6 w-full", className)}
      onClick={onClick}
      type={type ?? "button"}
      disabled={isLoading || disabled}
      variant={variant}
      size={size}
    >
      {isLoading ? (
        <div className="flex items-center justify-center gap-2">
          <Loader2Icon className="animate-spin" />
          Loading...
        </div>
      ) : (
        children
      )}
    </Button>
  );
};

export default CustomButton;
