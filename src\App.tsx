import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Routes, Route } from "react-router-dom";
import { lazy, Suspense } from "react";
import { ErrorBoundary } from "react-error-boundary";

import { TooltipProvider } from "./components/ui/tooltip";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import Layout from "./components/layout/Layout";
import ErrorPage from "./pages/ErrorPage";
import ProtectedRoute from "./components/common/ProtectedRoute";
import ResetPasswordPage from "./pages/ResetPasswordPage";
import LoadingOverlay from "./components/shared/LoadingOverlay";

const HomePage = lazy(() => import("./pages/HomePage"));
const DisalloRegistrationPage = lazy(
  () => import("./pages/DisalloRegistrationPage")
);
const NotFound = lazy(() => import("./pages/NotFound"));
const ProductDetailPage = lazy(() => import("./pages/ProductDetailPage"));
const LoginPage = lazy(() => import("./pages/LoginPage"));
const RegisterPage = lazy(() => import("./pages/RegisterPage"));
const RecoveryPasswordPage = lazy(() => import("./pages/RecoveryPasswordPage"));
const AddressPage = lazy(() => import("./pages/AddressPage"));
const OrdersPage = lazy(() => import("./pages/OrdersPage"));
const ProfilePage = lazy(() => import("./pages/ProfilePage"));
const OrderDetailPage = lazy(() => import("./pages/OrderDetailPage"));
const OrderSummaryPage = lazy(() => import("./pages/OrderSummaryPage"));
const CheckoutPage = lazy(() => import("./pages/CheckoutPage"));
const RefundPolicyPage = lazy(() => import("./pages/Policy/RefundPolicyPage"));
const CancellationPocilyPage = lazy(
  () => import("./pages/Policy/CancellationPocilyPage")
);
const PrivacyPolicyPage = lazy(
  () => import("./pages/Policy/PrivacyPolicyPage")
);
const TermAndConditionPage = lazy(
  () => import("./pages/Policy/TermAndConditionPage")
);
const CartPage = lazy(() => import("./pages/CartPage"));
const AuthCallbackPage = lazy(() => import("./pages/AuthCallbackPage"));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const publicRoutes = [
  { path: "login", element: <LoginPage /> },
  { path: "register", element: <RegisterPage /> },
  { path: "recovery-password/:email", element: <RecoveryPasswordPage /> },
  { path: "reset-password/", element: <ResetPasswordPage/>},
  { path: "cart", element: <CartPage /> },
  { path: "policy/privacy", element: <PrivacyPolicyPage /> },
  { path: "policy/term-and-condition", element: <TermAndConditionPage /> },
  { path: "policy/refund", element: <RefundPolicyPage /> },
  { path: "policy/cancellation", element: <CancellationPocilyPage /> },
  { path: "disallow-registration", element: <DisalloRegistrationPage /> },
  { path: "auth-callback", element: <AuthCallbackPage /> },
  {
    path: "product/:id",
    element: <ProductDetailPage />,
  },
  { path: "*", element: <NotFound /> },
];

const protectedRoutes = [
  { path: "profile", element: <ProfilePage /> },
  { path: "checkout", element: <CheckoutPage />, redirectPath: "/login" },
  { path: "orders", element: <OrdersPage /> },
  { path: "orders/:id", element: <OrderDetailPage /> },
  { path: "orders/summary/:id", element: <OrderSummaryPage /> },
  { path: "address", element: <AddressPage /> },
];

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <ErrorBoundary fallback={<ErrorPage />}>
          <Suspense fallback={<LoadingOverlay/>}>
            <Routes>
              <Route path="/" element={<Layout />}>
                <Route index element={<HomePage />} />
                {publicRoutes.map((route) => (
                  <Route
                    key={route.path}
                    path={route.path}
                    element={route.element}
                  />
                ))}

                {protectedRoutes.map((route) => (
                  <Route
                    key={route.path}
                    path={route.path}
                    element={
                      <ProtectedRoute redirectPath={route.redirectPath}>
                        {route.element}
                      </ProtectedRoute>
                    }
                  />
                ))}
              </Route>
            </Routes>
          </Suspense>
        </ErrorBoundary>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
