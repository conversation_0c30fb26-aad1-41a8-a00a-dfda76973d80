import { Label } from "@/components/ui/label";
import type { ModifierChoice } from "@/api/productApi";
import { Controller, type Control, type FieldValues } from "react-hook-form";

type TextProps<T extends FieldValues> = {
  options: ModifierChoice[];
  modifierText: string;
  forceAnswer: boolean;
  control: Control<T>;
  name: string;
};

const ModifierText = ({
  options,
  modifierText,
  forceAnswer,
  control,
  name,
}: TextProps<FieldValues>) => {
  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium mb-2">
        {modifierText} {forceAnswer && <span className="text-red-500">*</span>}
      </h3>

      {options.map((option, idx) => (
        <div key={option.choiceValue}>
          <Label>{option.choiceValue}</Label>
          <Controller
            name={`${name}.${idx}.choiceResult`}
            control={control}
            defaultValue=""
            render={({ field }) => (
              <input
                type="text"
                className="w-full mt-1 px-3 py-2 border rounded-md"
                placeholder={`Enter ${option.choiceValue}`}
                {...field}
              />
            )}
          />
        </div>
      ))}
    </div>
  );
};

export default ModifierText;
