import {
  alphabetLowerRegex,
  alphabetUpperRegex,
  minPasswordLength,
  numericRegex,
  specialCharRegex,
} from "@/constants/constant";
import { useCallback } from "react";
import { z, type RefinementCtx } from "zod";

const useValidationSchema = () => {
  const passwordSchema = z
    .string()
    .min(
      minPasswordLength,
      `Password must be at least ${minPasswordLength} characters`
    )
    .refine((val) => alphabetLowerRegex.test(val), {
      message: "Must include a lowercase letter",
    })
    .refine((val) => alphabetUpperRegex.test(val), {
      message: "Must include an uppercase letter",
    })
    .refine((val) => numericRegex.test(val), {
      message: "Must include a number",
    })
    .refine((val) => specialCharRegex.test(val), {
      message: "Must include a special character",
    });

  const confirmPasswordSchema = z
    .string()
    .min(
      minPasswordLength,
      `Password must be at least ${minPasswordLength} characters`
    );

  const validateConfirmMatch = useCallback(
    (
      registerPassword: string,
      registerConfirmPassword: string,
      ctx: RefinementCtx
    ) => {
      if (registerPassword !== registerConfirmPassword) {
        ctx.addIssue({
          code: "custom",
          path: ["confirmPassword"],
          message: "Passwords must match",
        });
      }
    },
    []
  );

  return {
    passwordSchema,
    confirmPasswordSchema,
    validateConfirmMatch,
  };
};
export default useValidationSchema;
