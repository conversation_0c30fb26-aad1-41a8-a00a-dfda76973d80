# Project Summary

This is a React e-commerce web application called "CPS Webstore" built with:

- Frontend: React with TypeScript using Vite as the build tool
- UI Components: Shadcn UI (based on Radix UI) with Tailwind CSS for styling
- State Management: Redux with RTK Query for API calls
- Routing: React Router for navigation
- Authentication: Custom auth flow with token storage
- Features:

  - Product browsing and filtering
  - User authentication
  - Shopping cart
  - Checkout process
  - Order management
  - User profile management

The application follows modern React patterns with context providers, lazy loading, and responsive design. It's containerized with Docker for deployment and includes CI/CD configuration via GitLab.

## Environment Configuration

| Variable | Description | Example |
|-----------|----------------------------|-------------------------------|
| `VITE_API_KEY` | API key for authorization | `3f2504e0-4f89-11d3-9a0c-0305e82c3301` |
| `VITE_CLIENT_ID` | OAuth Client ID | `onlineWebstore` |
| `VITE_CLIENT_SECRET` | OAuth Client Secret | `v4secret` |
| `VITE_IDENTITY_API` | Identity API base URL | `https://localhost:5105/identityapi/` |
| `VITE_REFERENCES_API` | References API base URL | `http://localhost:5112/referencesapi/` |
| `VITE_X_COMPONENT_ID` | Component identifier | `5` |
| `VITE_X_PRODUCT_ID` | Product identifier | `5` |
| `VITE_X_MODULE_ID` | Module identifier | `11` |
| `VITE_WHITELISTED_URLS` | Allowed CORS domains | `https://*.cps.golf,*localhost` |
| `VITE_REDIRECT_URL` | Redirect URL after authentication | `https://redirectportal-staging.cps.golf/` |
| `VITE_SITE_NAME` | Site name identifier | `webstore` |
| `VITE_BASE_PATH` | Base path for routing | `/webstore/` |

##### Example `.env.development`
```bash
VITE_API_KEY="3f2504e0-4f89-11d3-9a0c-0305e82c3301"
VITE_CLIENT_ID=onlineWebstore
VITE_CLIENT_SECRET=v4secret
VITE_IDENTITY_API=https://localhost:5105/identityapi/
VITE_REFERENCES_API=http://localhost:5112/referencesapi/
VITE_X_COMPONENT_ID=5
VITE_X_PRODUCT_ID=5
VITE_X_MODULE_ID=11
VITE_WHITELISTED_URLS=https://*.cps.golf,*localhost
VITE_REDIRECT_URL=https://redirectportal-staging.cps.golf/
VITE_SITE_NAME="webstore"
VITE_BASE_PATH="/webstore/"

