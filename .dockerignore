# Ignore node_modules (you will install fresh inside Docker)
node_modules

# Ignore local build output
dist

# Ignore logs and temp files
npm-debug.log
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.vscode
.idea
.DS_Store
*.swp
*.swo

# Environment and configuration files (optional)
.env
.env.local
.env.development

# Git
.git
.gitignore

# IDE settings
.vscode
.vs

# Docker
Dockerfile*
docker-compose*
.dockerignore