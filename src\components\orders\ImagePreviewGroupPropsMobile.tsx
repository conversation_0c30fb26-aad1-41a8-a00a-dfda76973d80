import DisplayImage from "./DisplayImage";

interface ImagePreviewGroupProps {
  images: string[];
  maxVisible?: number;
}

const ImagePreviewGroupMobile = ({
  images,
  maxVisible = 3,
}: ImagePreviewGroupProps) => {
  const visibleImages = images.slice(0, maxVisible);
  const remaining = images.length - maxVisible;

  return (
    <div className="grid grid-cols-2 gap-2">
      {visibleImages.map((img, index) => (
        <div
          key={index}
          className="w-full h-36 rounded-xl  overflow-hidden relative"
        >
          <DisplayImage img={img} key={`Preview ${index + 1}`} />
        </div>
      ))}

      {remaining > 0 && (
        <div className="w-full h-36 rounded-xl flex items-center justify-center bg-secondary text-xs font-semibold">
          +{remaining}
        </div>
      )}
    </div>
  );
};

export default ImagePreviewGroupMobile;
