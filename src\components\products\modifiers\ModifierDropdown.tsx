import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { ModifierChoice } from "@/api/productApi";
import { Controller, type Control } from "react-hook-form";

interface DropdownProps {
  options: ModifierChoice[];
  modifierText: string;
  forceAnswer: boolean;
  control: Control<any>;
  name: string;
}

export const ModifierDropdown = ({
  options,
  modifierText,
  forceAnswer,
  control,
  name,
}: DropdownProps) => {
  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium mb-2">
        {modifierText}{" "}
        {forceAnswer ? <span className="text-red-500">*</span> : null}
      </h3>

      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Select value={field.value} onValueChange={field.onChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select option" />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option.choiceValue} value={option.choiceValue}>
                  {option.choiceValue}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      />
    </div>
  );
};
