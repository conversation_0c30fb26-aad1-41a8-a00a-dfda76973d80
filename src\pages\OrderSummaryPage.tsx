import { Link, useParams } from "react-router-dom";
import { useGetOrderQuery } from "@/api/orderApi";
import { formatFullDateDetail, formatPrice } from "@/lib/utils";
import { Box, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import Card<PERSON>ogo, { type CardType } from "@/components/shared/CardLogo";

const OrderSummaryPage = () => {
  const { id } = useParams<{ id: string }>();
  const { data, isLoading } = useGetOrderQuery(id ?? "");

  if (isLoading) {
    return (
      <div className="px-4 py-10 mx-auto max-w-7xl">
        <div className="px-4 py-10 mx-auto max-w-7xl bg-white shadow-sm rounded-lg">
          <div className="max-w-3xl mx-auto text-center">
            <div className="mb-8 flex justify-center">
              <Skeleton className="h-16 w-16 rounded-full" />
            </div>
            <Skeleton className="h-8 w-80 mx-auto mb-4" />
            <Skeleton className="h-4 w-96 mx-auto mb-8" />

            <div className="mb-8 p-6 border rounded-lg">
              <Skeleton className="h-6 w-32 mx-auto mb-4" />
              <div className="space-y-1 text-sm mb-4">
                <Skeleton className="h-4 w-40 mx-auto" />
                <div className="divide-y mt-4">
                  {[1, 2, 3].map((item) => (
                    <div key={item} className="py-4 flex items-center">
                      <Skeleton className="h-16 w-16 rounded-md" />
                      <div className="ml-4 flex-1">
                        <Skeleton className="h-4 w-48 mb-2" />
                        <div className="flex items-center justify-between mt-1">
                          <Skeleton className="h-3 w-16" />
                          <Skeleton className="h-4 w-20" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="space-y-2 mt-4">
                  {[1, 2, 3, 4].map((item) => (
                    <div key={item} className="flex justify-between">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-4 w-16" />
                    </div>
                  ))}
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mt-16 text-left">
                {[1, 2, 3].map((item) => (
                  <div key={item}>
                    <Skeleton className="h-5 w-32 mb-2" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4 mt-1" />
                  </div>
                ))}
              </div>
            </div>
            <div className="flex justify-center mt-8 space-x-2">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-36" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="container px-4 py-10 mx-auto min-h-[600px] max-w-7xl">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-3xl font-bold mb-4">Order Not Found</h1>
          <p className="text-muted-foreground mb-8">
            We couldn't find the order you're looking for. Please check the
            order number and try again.
          </p>
          <Link to="/">
            <Button variant="default">Back to Shopping</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="px-4 py-10 mx-auto max-w-7xl ">
      <div className="px-4 py-10 mx-auto max-w-7xl bg-white shadow-sm rounded-lg">
        <div className="max-w-3xl mx-auto text-center">
          <div className="mb-8 flex justify-center">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <h1 className="text-3xl font-bold mb-4">
            Order Successfully Placed!
          </h1>
          <p className="text-muted-foreground mb-8">
            Your order has been received and is being processed. You will
            receive an email confirmation shortly.
          </p>
          <div className="mb-8 p-6 border rounded-lg">
            <h2 className="font-semibold text-2xl">Order #{data.orderId}</h2>
            <div className="space-y-1 text-sm mt-1">
              <p>Order Date: {formatFullDateDetail(data.orderDate)}</p>
              <div className="divide-y pt-2">
                {data.items.map((item) => (
                  <div key={item.itemCode} className="py-4 flex items-center">
                    <div className="h-16 w-16 flex-shrink-0 rounded-md border bg-gray-100 overflow-hidden">
                      {item.thumbnailImage ? (
                        <img
                          alt={item.imageName}
                          src={item.thumbnailImage}
                          className="h-full w-full object-fill object-center"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full w-full">
                          <Box className="text-gray-300 h-1/2 w-1/2 mx-auto" />
                        </div>
                      )}
                    </div>
                    <div className="ml-4 flex-1">
                      <h3 className="text-left text-base font-medium">
                        {item.itemDesc}
                      </h3>
                      {item.modifierMessage && (
                        <div className="text-left text-xs text-muted-foreground">
                          Modifier: {item.modifierMessage}
                        </div>
                      )}
                      {item.recipientEmail && (
                        <p className="text-left text-xs text-muted-foreground">
                          Recipient Email: {item.recipientEmail}
                        </p>
                      )}
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-sm">Qty: {item.quantity}</span>
                        <span className="text-base font-medium">
                          {formatPrice((item.price ?? 0) * item.quantity)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="space-y-2 mt-4">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>{formatPrice(data.subTotal ?? 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Shipping</span>
                  <span>{formatPrice(data.shipping ?? 0)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax</span>
                  <span>{formatPrice(data.tax ?? 0)}</span>
                </div>
                <div className="flex justify-between font-semibold pt-2 border-t">
                  <span>Total</span>
                  <span>{formatPrice(data.total ?? 0)}</span>
                </div>
              </div>
            </div>
            <div className="grid sm:grid-cols-3 gap-4 mt-16 text-left">
              <div>
                <h3 className="font-medium mb-2">Shipping Address</h3>
                <p className="text-sm">{data.shippingAddress}</p>
              </div>
              <div>
                <h3 className="font-medium mb-2">Billing Address</h3>
                <p className="text-sm">{data.billingAddress}</p>
              </div>
              <div>
                <h3 className="font-medium mb-2">Payment Method</h3>
                <div className="text-sm">
                  <CardLogo
                    cardType={
                      data.payment?.creditCardType?.toLocaleLowerCase() as CardType
                    }
                  />
                  <div>
                    {data.payment?.creditCardType} (•••• {data.payment?.cardNum}
                    )
                  </div>
                  <div className="text-xs">Exp: {data.payment?.cardExpire}</div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-center mt-8 space-x-2">
            <Link to="/orders">
              <Button variant="outline">View All Orders</Button>
            </Link>
            <Link to="/">
              <Button variant="default">Back to Shopping</Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderSummaryPage;
