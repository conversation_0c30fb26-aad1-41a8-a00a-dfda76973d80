import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

const DisalloRegistrationPage = () => {
  return (
    <div className="container px-4 py-10 mx-auto h-[600px] max-w-7xl">
      <div className="max-w-3xl mx-auto mt-8">
        <h2 className="text-3xl font-bold mb-4 text-center">
          New registrations are not accepted at this time.
        </h2>
        <p className="text-center">
          Sorry, we are not accepting new registrations at this time.
        </p>
        <p className="text-center">
          If you already have an account and are receiving this message in error
          please return to the login screen and try your email &amp; password
          again.
        </p>
      </div>
      <div className="flex justify-center mt-8">
        <Link to="/">
          <Button variant="default">Return to Home</Button>
        </Link>
      </div>
    </div>
  );
};

export default DisalloRegistrationPage;
