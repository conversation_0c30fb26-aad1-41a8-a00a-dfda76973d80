import { AuthService } from "@/auth/AuthService";
import { useEffect, useState } from "react";

export default function AuthCallbackPage() {
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string>("");

  useEffect(() => {
    console.log("=== OIDC Callback Debug Info ===");
    console.log("Current URL:", window.location.href);

    // Log the URL parameters for debugging
    const urlParams = new URLSearchParams(window.location.search);
    const urlParamsObj = Object.fromEntries(urlParams);
    console.log("URL parameters:", urlParamsObj);

    // Log all localStorage items
    console.log("=== ALL LocalStorage Items ===");
    const storageItems: Record<string, string> = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        const value = localStorage.getItem(key);
        storageItems[key] = value?.substring(0, 200) + (value && value.length > 200 ? '...' : '') || '';
        console.log(`${key}:`, value);
      }
    }

    // Check if state parameter exists in URL
    const stateFromUrl = urlParamsObj.state;
    console.log("State from URL:", stateFromUrl);

    // Look for matching state in localStorage with different possible patterns
    const possibleStateKeys = [
      `oidc.${stateFromUrl}`,
      `oidc.user:${window.location.origin}:${import.meta.env.VITE_CLIENT_ID}`,
      stateFromUrl
    ];

    console.log("Looking for state keys:", possibleStateKeys);
    possibleStateKeys.forEach(key => {
      const storedValue = localStorage.getItem(key);
      console.log(`${key}:`, storedValue);
    });

    const handleCallback = async () => {
      try {
        const authService = new AuthService();
        console.log("AuthService UserManager settings:", authService.userManager.settings);

        // Try to get the user first to see current state
        const existingUser = await authService.userManager.getUser();
        console.log("Existing user before callback:", existingUser);

        const user = await authService.userManager.signinRedirectCallback();
        console.log("User signed in successfully:", user);

        // Redirect to home page with base path
        const redirectUrl = import.meta.env.VITE_BASE_PATH || "/";
        console.log("Redirecting to:", redirectUrl);
        window.location.href = redirectUrl;
      } catch (error) {
        console.error("Error during signin callback:", error);
        console.error("Error details:", {
          name: error instanceof Error ? error.name : 'Unknown',
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        });

        const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
        setError(errorMessage);

        // Set debug info for display
        setDebugInfo(`
          URL: ${window.location.href}
          URL Params: ${JSON.stringify(urlParamsObj, null, 2)}
          LocalStorage Items: ${Object.keys(storageItems).length}
          State from URL: ${stateFromUrl}
          Error: ${errorMessage}
        `);
      }
    };

    handleCallback();
  }, []);

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <h1 className="text-xl font-bold text-red-600 mb-4">Authentication Error</h1>
        <p className="text-gray-600 mb-4">{error}</p>
        <details className="mb-4 w-full max-w-2xl">
          <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
            Show Debug Information
          </summary>
          <pre className="mt-2 p-4 bg-gray-100 rounded text-xs overflow-auto">
            {debugInfo}
          </pre>
        </details>
        <button
          onClick={() => window.location.href = import.meta.env.VITE_BASE_PATH || "/"}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Return to Home
        </button>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <div>Signing in...</div>
      </div>
    </div>
  );
}
