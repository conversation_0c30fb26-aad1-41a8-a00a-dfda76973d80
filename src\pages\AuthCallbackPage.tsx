import { AuthService } from "@/auth/AuthService";
import { useEffect, useState } from "react";

export default function AuthCallbackPage() {
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log("Callback storage", localStorage);
    console.log("Current URL:", window.location.href);

    const handleCallback = async () => {
      try {
        // Log the URL parameters for debugging
        const urlParams = new URLSearchParams(window.location.search);
        console.log("URL parameters:", Object.fromEntries(urlParams));

        const authService = AuthService.getInstance();
        const user = await authService.userManager.signinRedirectCallback();
        console.log("User signed in successfully:", user);

        // Redirect to home page
        window.location.href = import.meta.env.VITE_BASE_PATH || "/";
      } catch (error) {
        console.error("Error during signin callback:", error);
        setError(error instanceof Error ? error.message : "Unknown error occurred");
      }
    };

    handleCallback();
  }, []);

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-xl font-bold text-red-600 mb-4">Authentication Error</h1>
        <p className="text-gray-600 mb-4">{error}</p>
        <button
          onClick={() => window.location.href = import.meta.env.VITE_BASE_PATH || "/"}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Return to Home
        </button>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <div>Signing in...</div>
      </div>
    </div>
  );
}
