import { userManager } from "@/auth/oidcConfig";
import { useEffect } from "react";

export default function AuthCallbackPage() {
  useEffect(() => {
    const handleCallback = async () => {
      try {
        await userManager.signinRedirectCallback();
        window.location.href = "/";
      } catch (error) {
        console.error("Error during signin callback:", error);
        // Optional: redirect to error page or show a message
      }
    };

    handleCallback();
  }, []);

  return <div>Signing in...</div>;
}
