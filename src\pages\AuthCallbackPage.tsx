import { AuthService } from "@/auth/AuthService";
import { useEffect } from "react";

export default function AuthCallbackPage() {
  useEffect(() => {
    console.log("Callback storage", localStorage);
    const handleCallback = async () => {
      try {
        await new AuthService().userManager.signinRedirectCallback();
        window.location.href = "/";
      } catch (error) {
        console.error("Error during signin callback:", error);
        // Optional: redirect to error page or show a message
      }
    };

    handleCallback();
  }, []);

  return <div>Signing in...</div>;
}
