import { useForm, useWatch, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { zipCodeRegex } from "@/lib/regexPattern";
import FieldInput from "@/components/input/FieldInput";
import { CheckboxField } from "@/components/input/CheckbokField";
import { Button } from "@/components/ui/button";
import { FieldSelector } from "@/components/input/FieldSelector";
import { countries } from "@/data/countryRegions";
import { useCallback, useEffect, useMemo } from "react";
import { mapToOption, type Option } from "@/lib/mapToOption";
import type { ShippingAddressViewModel } from "@/api/models";
import { toast } from "@/hooks/use-toast";
import { useUserProfile } from "@/hooks/useUserProfile";

const ShippingAddressForm = ({
  id,
  onCancel = () => {},
  disableSetIsDefault = false,
}: {
  id?: number;
  onCancel?: () => void;
  disableSetIsDefault?: boolean;
}) => {
  const { userProfile, updateShippingAddress, addShippingAddress, loading } =
    useUserProfile();

  const existingShippingAddress = useMemo(
    () => userProfile?.userAddress.shippingAddresses.find((b) => b.id === id),
    [id, userProfile?.userAddress.shippingAddresses]
  );

  const form = useForm<ShippingAddressViewModel>({
    defaultValues: {
      id: 0,
      firstName: "",
      lastName: "",
      street: "",
      city: "",
      zip: "",
      state: "",
      country: "",
      isDefault: true,
    },
    resolver: zodResolver(
      z.object({
        firstName: z.string().min(1, "First name is required."),
        lastName: z.string().min(1, "Last name is required."),
        street: z.string().min(1, "Street is required."),
        city: z.string().min(1, "City is required."),
        state: z.string().min(1, "State is required."),
        zip: z
          .string()
          .min(1, "Zip is required.")
          .refine((val) => !val || zipCodeRegex.test(val), {
            message: "Invalid zip code format.",
          }),
        country: z.string().min(1, "Country is required."),
        isDefault: z.boolean().optional(),
      })
    ),
  });

  const { control, handleSubmit, reset } = form;
  const [shippingAddressId, isDefault] = useWatch({
    control,
    name: ["id", "isDefault"],
  });

  const selectedCountry = useWatch({ control, name: "country" });

  const countriesOption = useMemo<Option[]>(
    () =>
      mapToOption(
        countries,
        "countryName",
        (i) => `${i.countryName} (${i.countryShortCode})`
      ),
    []
  );

  const stateOption = useMemo<Option[]>(
    () =>
      selectedCountry
        ? mapToOption(
            countries.find((c) => c.countryName === selectedCountry)?.regions ??
              [],
            "name",
            "name"
          )
        : [],
    [selectedCountry]
  );

  const onSubmit = useCallback<SubmitHandler<ShippingAddressViewModel>>(
    async (formData) => {
      try {
        const request: ShippingAddressViewModel = {
          ...formData,
          isDefault,
          id: shippingAddressId,
          email: userProfile?.email ?? "",
          mobilePhone: userProfile?.mobile_phone ?? "",
        };

        if (!id) {
          await addShippingAddress(request);
        } else {
          await updateShippingAddress(request.id, request);
        }

        toast({ title: "✅ Update Profile shipping address successfully" });
        onCancel();
      } catch (err) {
        console.error("Unable to update shipping address", err);
        toast({ title: "❌ Unable to update shipping address" });
      }
    },
    [
      addShippingAddress,
      id,
      isDefault,
      onCancel,
      shippingAddressId,
      updateShippingAddress,
      userProfile?.email,
      userProfile?.mobile_phone,
    ]
  );

  useEffect(() => {
    if (!existingShippingAddress) return;
    reset(existingShippingAddress);
  }, [existingShippingAddress, reset]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 py-4">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <FieldInput<ShippingAddressViewModel>
          name="firstName"
          control={control}
          label="First name"
          type="text"
        />
        <FieldInput<ShippingAddressViewModel>
          name="lastName"
          control={control}
          label="Last name"
          type="text"
        />
      </div>
      <FieldSelector<ShippingAddressViewModel>
        name="country"
        control={control}
        label="Country"
        options={countriesOption}
      />
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <FieldSelector<ShippingAddressViewModel>
          name="state"
          control={control}
          label="State / Province"
          options={stateOption}
        />
        <FieldInput<ShippingAddressViewModel>
          name="city"
          control={control}
          label="City"
          type="text"
        />
      </div>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <FieldInput<ShippingAddressViewModel>
          name="zip"
          control={control}
          label="ZIP / Postal code"
        />
        <FieldInput<ShippingAddressViewModel>
          name="street"
          control={control}
          label="Street address"
          type="text"
        />
      </div>
      <div className="pt-2">
        <CheckboxField<ShippingAddressViewModel>
          name="isDefault"
          control={control}
          label="Set as default address"
          disabled={disableSetIsDefault}
        />
      </div>
      <div className="flex justify-end space-x-2 mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={() => {
            onCancel();
          }}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          Update Address
        </Button>
      </div>
    </form>
  );
};

export default ShippingAddressForm;
