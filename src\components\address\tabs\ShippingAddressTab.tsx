import type { ShippingAddressViewModel } from "@/api/models";
import AddressDialog from "@/components/profiles/AddressDialog";
import ConfirmDeleteDialog from "@/components/profiles/ConfirmDeleteDialog";
import NoAddresses from "@/components/shared/NoAddresses";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { useUserProfile } from "@/hooks/useUserProfile";
import { Pencil, Plus, Trash2 } from "lucide-react";
import { useCallback, useMemo, useState } from "react";

const ShippingAddressTab = () => {
  const {
    userProfile,
    deleteShippingAddress,
    loading,
  } = useUserProfile();

  const addresses = useMemo(
    () => userProfile?.userAddress.shippingAddresses ?? [],
    [userProfile?.userAddress.shippingAddresses]
  );

  const [dialog, setDialog] = useState<"modify" | "confirmDelete" | "none">("none");
  const [editAddress, setEditAddress] = useState<ShippingAddressViewModel>();
  const [removeAddressId, setRemoveAddressId] = useState<number>();

  const handleAdd = () => {
    setEditAddress(undefined);
    setDialog("modify");
  };

  const handleEdit = (address: ShippingAddressViewModel) => {
    setEditAddress(address);
    setDialog("modify");
  };

  const handleRemove = (id: number) => {
    setRemoveAddressId(id);
    setDialog("confirmDelete");
  };

  const closeDialog = () => setDialog("none");

  const onDeleteShipping = useCallback(async () => {
    try {
      if (!removeAddressId) return;
      await deleteShippingAddress(removeAddressId);
      toast({ title: "✅ Shipping address deleted successfully." });
      closeDialog();
    } catch (err) {
      console.error("Unable to delete shipping address", err);
      toast({ title: "❌ Unable to delete shipping address" });
    }
  }, [removeAddressId, deleteShippingAddress]);

  return (
    <div className="md:col-span-3">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Shipping Addresses</h2>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add New Address
        </Button>
      </div>

      {loading ? (
        <>...loading</>
      ) : addresses.length === 0 ? (
        <NoAddresses />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {addresses.map((address) => (
            <div key={address.id} className="border rounded-lg p-6 relative">
              {address.isDefault && (
                <div className="absolute top-2 right-2">
                  <span className="px-2 py-1 bg-green-600 text-white text-xs rounded-md font-medium">
                    Default
                  </span>
                </div>
              )}
              <h3 className="font-medium mb-2">
                {address.firstName} {address.lastName}
              </h3>
              <div className="text-sm text-muted-foreground space-y-1">
                <p>{address.street}</p>
                <p>
                  {address.city}, {address.state} {address.zip}
                </p>
                <p>{address.country}</p>
              </div>
              <div className="mt-4 flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEdit(address)}
                >
                  <Pencil className="h-3.5 w-3.5 mr-1" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-destructive border-destructive hover:bg-destructive/90 hover:text-destructive-foreground"
                  onClick={() => handleRemove(address.id)}
                >
                  <Trash2 className="h-3.5 w-3.5 mr-1" />
                  Delete
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}

      <AddressDialog
        form="shipping"
        addressId={editAddress?.id}
        isOpen={dialog === "modify"}
        onClose={closeDialog}
        title={editAddress ? "Edit Address" : "Add New Address"}
      />

      <ConfirmDeleteDialog
        title="Confirm Delete Shipping Address"
        isOpen={dialog === "confirmDelete"}
        onClose={closeDialog}
        onDelete={onDeleteShipping}
        message="Are you sure you want to delete this shipping address? This action cannot be undone."
      />
    </div>
  );
};

export default ShippingAddressTab;