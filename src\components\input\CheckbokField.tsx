import { Controller, type Control, type FieldValues, type Path } from "react-hook-form";

type CheckboxFieldProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>; // Or you can pass your specific form type
  label: string;
  disabled?: boolean;
  className?: string;
};

export const CheckboxField = <T extends FieldValues>({
  name,
  control,
  label,
  disabled = false,
  className = "",
}: CheckboxFieldProps<T>): JSX.Element => {
  return (
    <Controller
      name={name}
      control={control}
      render={({
        field: { value, onChange, ref },
        fieldState: { error },
      }) => (
        <label className={`flex items-center gap-2 ${className}`}>
          <input
            type="checkbox"
            ref={ref}
            checked={!!value}
            onChange={(e) => onChange(e.target.checked)}
            disabled={disabled}
            className="h-4 w-4"
          />
          <span>{label}</span>
          {error && <p className="text-red-500 text-sm">{error.message}</p>}
        </label>
      )}
    />
  );
};