import DisplayImage from "./DisplayImage";

interface ImagePreviewGroupProps {
  images: string[];
  maxVisible?: number;
}

const zIndexes = ["z-10", "z-20", "z-30", "z-40", "z-50"]; // up to 5 images

const ImagePreviewGroup = ({
  images,
  maxVisible = 3,
}: ImagePreviewGroupProps) => {
  const visibleImages = images.slice(0, maxVisible);
  const remaining = images.length - maxVisible;

  return (
    <div className="flex items-center">
      {visibleImages.map((img, index) => (
        <div
          key={index}
          className={`w-14 h-14 rounded-xl border-4 border-white overflow-hidden relative ${
            index > 0 ? "-ml-5" : ""
          } ${zIndexes[index] || "z-50"}`}
        >
          <DisplayImage img={img} key={`Preview ${index + 1}`} />
        </div>
      ))}

      {remaining > 0 && (
        <div
          className={`w-14 h-14 rounded-xl border-4 bg-gray-100 border-white flex items-center justify-center text-xs font-semibold text-gray-700 -ml-5 z-40`}
        >
          +{remaining}
        </div>
      )}
    </div>
  );
};

export default ImagePreviewGroup;
