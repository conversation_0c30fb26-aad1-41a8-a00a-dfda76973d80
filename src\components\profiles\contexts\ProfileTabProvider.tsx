import type { Address } from "@/types";
import { useCallback, useState, type ReactNode } from "react";
import { ProfileContext, type editProfileMode } from "./ProfileContext";

export const ProfileTabProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [editTab, setEditTab] = useState<editProfileMode>("none");

  const displayAddress = useCallback((address: Address | undefined) => {
    if (!address) return "-";
    return [
      `${address.firstName} ${address.lastName}`,
      address.street,
      address.city,
      `${address.state} ${address.zip}`,
      address.country,
    ]
      .filter(Boolean)
      .join(" ");
  },[])

  return (
    <ProfileContext.Provider
      value={{
        editTab,
        onEditTab: setEditTab,
        displayAddress
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
};
export default ProfileTabProvider