import {
  type ReactNode,
  useState,
  useMemo,
  useEffect,
  useCallback,
} from "react";
import { UserProfileContext } from "./UserProfileContext";
import type {
  BillAddressViewModel,
  ShippingAddressViewModel,
  WebstoreProfileViewModel,
} from "@/api/models";
import {
  useDeleteCardOnFileMutation,
  useLazyGetUserProfileQuery,
  useSetDefaultCardOnFileMutation,
  useUpdateBillingAddressMutation,
  useCreateBillingAddressMutation,
  useDeleteBillingAddressMutation,
  useUpdateShippingAddressMutation,
  useCreateShippingAddressMutation,
  useDeleteShippingAddressMutation,
  useUpdateUserProfileMutation,
  useSetDefaultBillingAddressMutation,
  useSetDefaultShippingAddressMutation,
} from "@/api/userProfileApi";
import { useAuth } from "@/hooks/useAuth";
import type { UserProfile } from "@/types";

export const UserProfileProvider = ({ children }: { children: ReactNode }) => {
  const [userProfile, setUserProfile] = useState<UserProfile>();

  // Queries & Mutations
  const [fetchUserProfile, { isLoading: isLoadingUserProfile }] =
    useLazyGetUserProfileQuery();
  const [
    setDefaultCardOnFileMutation,
    { isLoading: isLoadingSetDefaultCardOnFile },
  ] = useSetDefaultCardOnFileMutation();
  const [deleteCardOnFileMutation, { isLoading: isLoadingDeleteCardOnFile }] =
    useDeleteCardOnFileMutation();
  const { isAuthenticated, user } = useAuth();

  const [updateProfile, { isLoading: isUpdatingUserProfile }] =
    useUpdateUserProfileMutation();
  const [updateBilling, { isLoading: isUpdatingBilling }] =
    useUpdateBillingAddressMutation();
  const [createBilling, { isLoading: isCreatingBilling }] =
    useCreateBillingAddressMutation();
  const [deleteBilling, { isLoading: isDeletingBilling }] =
    useDeleteBillingAddressMutation();
  const [updateShipping] = useUpdateShippingAddressMutation();
  const [createShipping, { isLoading: isCreatingShipping }] =
    useCreateShippingAddressMutation();
  const [deleteShipping, { isLoading: isDeletingShipping }] =
    useDeleteShippingAddressMutation();
  const [setDefaultBilling, { isLoading: isSettingDefaultBilling }] =
    useSetDefaultBillingAddressMutation();
  const [setDefaultShipping, { isLoading: isSettingDefaultShipping }] =
    useSetDefaultShippingAddressMutation();

  // User
  const updateUserProfile = useCallback(
    async (profile: WebstoreProfileViewModel) => {
      try {
        await updateProfile(profile).unwrap();
        setUserProfile((prev) => {
          if (!prev) return prev;
          return {
            ...prev,
            first_name: profile.first,
            last_name: profile.last,
            email: profile.email,
            mobile_phone: profile.mobilePhone,
          };
        });
      } catch (err) {
        console.error("Error updating user profile", err);
        throw err;
      }
    },
    [updateProfile]
  );

  // Billing address
  const updateBillingAddress = useCallback(
    async (id: number, address: BillAddressViewModel) => {
      try {
        await updateBilling({ id, address }).unwrap();
        setUserProfile((prev) => {
          if (!prev) return prev;
          const updatedAddresses = prev.userAddress.billingAddresses.map(
            (addr) => (addr.webstoreBillingAddressId === id ? address : addr)
          );
          return {
            ...prev,
            userAddress: {
              ...prev.userAddress,
              billingAddresses: updatedAddresses,
            },
          };
        });
      } catch (err) {
        console.error("Error updating billing address", err);
        throw err;
      }
    },
    [updateBilling]
  );

  const addBillingAddress = useCallback(
    async (newAddress: BillAddressViewModel) => {
      try {
        const newId = await createBilling(newAddress).unwrap();
        setUserProfile((prev) => {
          if (!prev) return prev;
          const billingAddresses = prev.userAddress.billingAddresses ?? [];
          return {
            ...prev,
            userAddress: {
              ...prev.userAddress,
              billingAddresses: [
                ...billingAddresses,
                { ...newAddress, webstoreBillingAddressId: newId },
              ].map((i) =>
                newAddress.isDefault
                  ? { ...i, isDefault: i.webstoreBillingAddressId === newId }
                  : i
              ),
            },
          };
        });
      } catch (err) {
        console.error("Error creating billing address", err);
        throw err;
      }
    },
    [createBilling]
  );

  const deleteBillingAddress = useCallback(
    async (id: number) => {
      try {
        await deleteBilling(id).unwrap();
        setUserProfile((prev) => {
          if (!prev) return prev;
          const updatedAddresses = prev.userAddress.billingAddresses.filter(
            (addr) => addr.webstoreBillingAddressId !== id
          );
          return {
            ...prev,
            userAddress: {
              ...prev.userAddress,
              billingAddresses: updatedAddresses,
            },
          };
        });
      } catch (err) {
        console.error("Error deleting billing address", err);
        throw err;
      }
    },
    [deleteBilling]
  );

  const setDefaultBillingAddress = useCallback(
    async (id: number) => {
      try {
        await setDefaultBilling(id).unwrap();
        setUserProfile((prev) => {
          if (!prev) return prev;
          const updatedAddresses = prev.userAddress.billingAddresses.map(
            (addr) => ({
              ...addr,
              isDefault: addr.webstoreBillingAddressId === id,
            })
          );
          return {
            ...prev,
            userAddress: {
              ...prev.userAddress,
              billingAddresses: updatedAddresses,
            },
          };
        });
      } catch (err) {
        console.error("Error set default billing address", err);
        throw err;
      }
    },
    [setDefaultBilling]
  );

  // Shipping
  const setDefaultShippingAddress = useCallback(
    async (id: number) => {
      try {
        await setDefaultShipping(id).unwrap();
        setUserProfile((prev) => {
          if (!prev) return prev;
          const updatedAddresses = prev.userAddress.shippingAddresses.map(
            (addr) => ({
              ...addr,
              isDefault: addr.id === id,
            })
          );
          return {
            ...prev,
            userAddress: {
              ...prev.userAddress,
              shippingAddresses: updatedAddresses,
            },
          };
        });
      } catch (err) {
        console.error("Error set default billing address", err);
        throw err;
      }
    },
    [setDefaultShipping]
  );

  const updateShippingAddress = useCallback(
    async (id: number, address: ShippingAddressViewModel) => {
      try {
        await updateShipping({ id, address }).unwrap();
        setUserProfile((prev) => {
          if (!prev) return prev;
          const updatedAddresses = prev.userAddress.shippingAddresses.map(
            (addr) => (addr.id === id ? address : addr)
          );
          return {
            ...prev,
            userAddress: {
              ...prev.userAddress,
              shippingAddresses: updatedAddresses,
            },
          };
        });
      } catch (err) {
        console.error("Error updating shipping address", err);
        throw err;
      }
    },
    [updateShipping]
  );

  const addShippingAddress = useCallback(
    async (newAddress: ShippingAddressViewModel) => {
      try {
        const newId = await createShipping(newAddress).unwrap();
        setUserProfile((prev) => {
          if (!prev) return prev;
          const shippingAddresses = prev.userAddress.shippingAddresses ?? [];
          return {
            ...prev,
            userAddress: {
              ...prev.userAddress,
              shippingAddresses: [
                ...shippingAddresses,
                { ...newAddress, id: newId },
              ].map((i) =>
                newAddress.isDefault ? { ...i, isDefault: i.id === newId } : i
              ),
            },
          };
        });
      } catch (err) {
        console.error("Error creating shipping address", err);
        throw err;
      }
    },
    [createShipping]
  );

  const deleteShippingAddress = useCallback(
    async (id: number) => {
      try {
        await deleteShipping(id).unwrap();
        setUserProfile((prev) => {
          if (!prev) return prev;
          const updatedAddresses = prev.userAddress.shippingAddresses.filter(
            (addr) => addr.id !== id
          );
          return {
            ...prev,
            userAddress: {
              ...prev.userAddress,
              shippingAddresses: updatedAddresses,
            },
          };
        });
      } catch (err) {
        console.error("Error deleting shipping address", err);
        throw err;
      }
    },
    [deleteShipping]
  );

  const setDefaultCardOnFile = useCallback(
    async (id: number) => {
      return setDefaultCardOnFileMutation({ id })
        .unwrap()
        .then((response) => {
          const updatedCards = userProfile?.creditCards.map((card) => ({
            ...card,
            isDefault: card.id === id,
          }));
          setUserProfile((prev) => {
            if (!prev) return prev;
            return {
              ...prev,
              creditCards: updatedCards ?? prev.creditCards,
            };
          });
          return response;
        });
    },
    [setDefaultCardOnFileMutation, userProfile?.creditCards]
  );

  const deleteCardOnFile = useCallback(
    async (id: number) => {
      return deleteCardOnFileMutation({ id })
        .unwrap()
        .then((response) => {
          const updatedCards = userProfile?.creditCards.filter(
            (card) => card.id !== id
          );
          setUserProfile((prev) => {
            if (!prev) return prev;
            return {
              ...prev,
              creditCards: updatedCards ?? prev.creditCards,
            };
          });
          return response;
        });
    },
    [deleteCardOnFileMutation, userProfile?.creditCards]
  );

  const getUserProfile = useCallback(async () => {
    const profile = await fetchUserProfile().unwrap();
    setUserProfile({
      ...profile,
      id: user?.id ?? "",
      first_name: user?.first_name ?? "",
      last_name: user?.last_name ?? "",
      mobile_phone: user?.mobile_phone ?? "",
      email: user?.email ?? "",
    });
  }, [
    fetchUserProfile,
    user?.email,
    user?.first_name,
    user?.id,
    user?.last_name,
    user?.mobile_phone,
  ]);

  useEffect(() => {
    if (!isAuthenticated) return;
    getUserProfile();
  }, [isAuthenticated, getUserProfile]);

  const isLoading =
    isLoadingUserProfile ||
    isCreatingBilling ||
    isUpdatingBilling ||
    isDeletingBilling ||
    isCreatingShipping ||
    isDeletingShipping ||
    isUpdatingUserProfile ||
    isLoadingSetDefaultCardOnFile ||
    isLoadingDeleteCardOnFile;

  const contextValue = useMemo(
    () => ({
      userProfile: userProfile ?? null,
      loading: isLoading,
      updateBillingAddress,
      updateShippingAddress,
      addBillingAddress,
      addShippingAddress,
      deleteBillingAddress,
      deleteShippingAddress,
      updateUserProfile,
      setDefaultBillingAddress,
      setDefaultShippingAddress,
      defaultShippingLoading: isSettingDefaultBilling,
      defaultBillingLoading: isSettingDefaultShipping,
      setDefaultCardOnFile,
      deleteCardOnFile,
      getUserProfile,
    }),
    [
      userProfile,
      isLoading,
      updateBillingAddress,
      updateShippingAddress,
      addBillingAddress,
      addShippingAddress,
      deleteBillingAddress,
      deleteShippingAddress,
      updateUserProfile,
      setDefaultBillingAddress,
      setDefaultShippingAddress,
      isSettingDefaultBilling,
      isSettingDefaultShipping,
      setDefaultCardOnFile,
      deleteCardOnFile,
      getUserProfile,
    ]
  );

  return (
    <UserProfileContext.Provider value={contextValue}>
      {children}
    </UserProfileContext.Provider>
  );
};
