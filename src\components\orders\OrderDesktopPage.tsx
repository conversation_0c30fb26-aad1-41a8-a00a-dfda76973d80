import type { WebstoreOrderHistoryViewModel } from "@/api/models/webstoreOrderHistoryViewModel";
import DataTable, { type DataTableColumn } from "../shared/DataTable";
import ImagePreviewGroup from "./ImagePreviewGroup";
import { formatCurrency } from "@/lib/formatCurrency";
import { dateFormat } from "@/lib/dateFormat";
import { useNavigate } from "react-router-dom";
import { useGetOrderHistoryQuery } from "@/api/orderApi";
import { useState } from "react";
import LoadingOverlay from "../shared/LoadingOverlay";

const pageSize = 10;

const cols: DataTableColumn<WebstoreOrderHistoryViewModel>[] = [
  {
    header: "",
    key: "orderItems",
    render: (_, row) => (
      <div className="flex items-center gap-2">
        <ImagePreviewGroup
          images={row.orderItems.map((i) => i.thumbnailImage)}
        />
      </div>
    ),
  },
  {
    header: "Order",
    key: "orderNo",
  },
  {
    header: "Status",
    key: "orderStatus",
    render: (_, row) => (
      <div>
        <p>{row.orderStatus}</p>
        <span className="text-gray-500 text-sm">
          {dateFormat(row.orderDate, "MMM d, yyyy")}
        </span>
      </div>
    ),
    rowClassName: "flex flex-col",
  },
  {
    header: "Total Amount",
    key: "total",
    render: (_, row) => <p>{formatCurrency(row.total, "en-US")}</p>,
  },
];

const OrderDesktopPage = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const onClickRow = (row: WebstoreOrderHistoryViewModel) =>
    navigate(`/Orders/${row.orderID}`);

  const { data, isLoading } = useGetOrderHistoryQuery({
    pageSize,
    currentPage,
  });
  const handleNext = () => setCurrentPage((p) => p + 1);
  const handlePrevious = () => setCurrentPage((p) => Math.max(p - 1, 1));

  if (isLoading) return <LoadingOverlay />;

  if (!data?.items)
    return (
      <div className="flex h-full min-h-screen items-center justify-center">
        No order history
      </div>
    );

  return (
    <div className="px-4 py-10 mx-auto max-w-7xl ">
      <div>
        <h2 className="text-2xl font-bold sm:text-3xl m-6">Orders</h2>
      </div>

      <DataTable
        data={data.items}
        columns={cols}
        onClickRow={onClickRow}
        pageSize={pageSize}
        totalData={data.totalItems}
        onNext={handleNext}
        onPrevious={handlePrevious}
        currentPage={currentPage}
      />
    </div>
  );
};
export default OrderDesktopPage;
