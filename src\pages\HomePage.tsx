import { Link, useLocation } from "react-router-dom";
import { ArrowRight } from "lucide-react";
import ProductCard from "@/components/products/ProductCard";
import { useOptions } from "@/hooks/useOptions";
import Navbar from "@/components/layout/Navbar";
import { useProducts } from "@/hooks/useProducts";
import queryString from "query-string";
import { useEffect, useState } from "react";
import type { WebstoreProduct } from "@/api/productApi";
import Banner from "@/components/products/Banner";
import { Skeleton } from "@/components/ui/skeleton";

const HomePage = () => {
  const { options } = useOptions();
  const banners = options?.banners || [];
  const { getFilteredProducts, loading: productsLoading } = useProducts();
  const location = useLocation();
  const { department } = queryString.parse(location.search);
  const [filteredProducts, setFilteredProducts] = useState<WebstoreProduct[]>(
    []
  );

  useEffect(() => {
    const products = getFilteredProducts(department as string);
    setFilteredProducts(products);
  }, [location.search, getFilteredProducts, department]);

  if (productsLoading) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="max-w-7xl mx-auto container px-4">
          <div className="py-8 mb-4">
            <Skeleton className="w-full h-[400px] rounded-lg" />
          </div>
          <section className="my-8">
            <div className="container">
              <div className="flex items-center justify-between mb-8">
                <Skeleton className="h-8 w-48" />
                <Skeleton className="h-6 w-24" />
              </div>
              <div className="grid sm:grid-cols-2 gap-4 lg:gap-6 md:grid-cols-3 lg:grid-cols-4">
                {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
                  <div key={item} className="space-y-3">
                    <Skeleton className="h-[200px] w-full rounded-lg" />
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                ))}
              </div>
            </div>
          </section>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Category */}
      <Navbar />

      <div className="max-w-7xl mx-auto container px-4">
        {/* Products Slider */}
        <Banner banners={banners} />

        {/* Featured Products Section */}
        <section className="my-8">
          <div className="container mx-auto">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold sm:text-3xl">
                {department ?? "Newest Additions"}
              </h2>
              <Link
                to="/"
                className="flex items-center text-sm font-medium hover:text-primary"
              >
                View All
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </div>
            <div className="grid sm:grid-cols-2 gap-4 lg:gap-6 md:grid-cols-3 lg:grid-cols-4">
              {filteredProducts?.length > 0 ? (
                filteredProducts?.map((product) => (
                  <ProductCard key={product.itemCode} product={product} />
                ))
              ) : (
                <div className="col-span-full text-center text-xl text-gray-500 min-h-[484px] flex items-center justify-center">
                  No products available.
                </div>
              )}
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default HomePage;
