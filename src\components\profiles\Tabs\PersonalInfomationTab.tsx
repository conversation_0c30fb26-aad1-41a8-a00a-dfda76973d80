import { useMemo } from "react";
import { useProfileTab } from "../hooks/useProfileTab";
import EditButton from "../EditButton";
import { useUserProfile } from "@/hooks/useUserProfile";
import PersonalInformationForm from "./PersonalInformationForm";

const PersonalInformationTab = () => {
  const { userProfile } = useUserProfile();
  const { editTab, onEditTab } = useProfileTab();

  const hasPersonalInfo = useMemo(
    () =>
      userProfile?.first_name ||
      userProfile?.last_name ||
      userProfile?.email ||
      userProfile?.mobile_phone,
    [userProfile]
  );

  return (
    <div className="border rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold">Personal Information</h2>
        {editTab === "none" && <EditButton mode="personal" />}
      </div>

      {editTab === "personal" ? (
        <PersonalInformationForm onCancel={() => onEditTab("none")} />
      ) : !hasPersonalInfo ? (
        <>-</>
      ) : (
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <InfoItem label="First name" value={userProfile?.first_name} />
            <InfoItem label="Last name" value={userProfile?.last_name} />
          </div>
          <InfoItem label="Email" value={userProfile?.email} />
          <InfoItem label="Phone" value={userProfile?.mobile_phone} />
        </div>
      )}
    </div>
  );
};

export default PersonalInformationTab;

const InfoItem = ({ label, value }: { label: string; value?: string }) => (
  <div>
    <p className="text-sm text-muted-foreground">{label}</p>
    <p className="font-medium">{value?.trim() || "-"}</p>
  </div>
);
