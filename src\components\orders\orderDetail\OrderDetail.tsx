import type { OrderItemViewModel } from "@/api/models/orderDetailViewModel";
import { formatCurrency } from "@/lib/formatCurrency";
import { Box } from "lucide-react";
import { Link } from "react-router-dom";

const OrderDetail = ({ items }: { items: OrderItemViewModel[] }) => {
  return (
    <div className="border rounded-lg p-6">
      <h2 className="text-lg font-semibold mb-4">Items in Your Order</h2>
      <div className="divide-y">
        {items.map((item: OrderItemViewModel) => (
          <div key={item.orderItemID} className="py-4 flex">
            <div className="h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border bg-gray-100">
              {item.thumbnailImage ? (
                <img
                  src={item.thumbnailImage}
                  alt={item.itemDesc}
                  className="h-full w-full object-cover object-center"
                />
              ) : (
                <div className="flex items-center justify-center h-full w-full">
                  <Box className="text-gray-300 group-hover:text-gray-800 transition-colors h-1/2 w-1/2 mx-auto" />
                </div>
              )}
            </div>
            <div className="ml-4 flex flex-1 flex-col">
              <div>
                <div className="flex justify-between text-base font-medium">
                  <h3>
                    <Link
                      to={`/product/${item.itemCode}`}
                      className="hover:text-primary"
                    >
                      {item.itemDesc}
                    </Link>
                  </h3>
                  <p className="ml-4">{formatCurrency(item.price, "en-US")}</p>
                </div>
                <p className="mt-1 text-sm text-muted-foreground">
                  {item.itemDesc}
                </p>
              </div>
              <div className="flex flex-1 items-end justify-between text-sm">
                <p className="text-muted-foreground">Qty {item.quantity}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
export default OrderDetail;
