export interface OrderDetailViewModel {
    orderId: number
    orderDate: Date
    orderNo: string
    orderStatus: string
    subTotal: number
    shipping: number
    tax: number
    total: number
    shippingAddress: string
    billingAddress: string
    payment: OrderPaymentViewModel
    items: OrderItemViewModel[]
}

export interface OrderItemViewModel {
    orderItemID: number
    itemDesc: string
    price: number
    quantity: number
    itemCode: string
    thumbnailImage: string
    recipientEmail: string
    recipientName: string
    modifierMessage: string
}

export interface OrderPaymentViewModel {
    orderPaymentId: number
    cardNum: string
    cardExpire: string
    creditCardType: string
}