import type { CartItem, AddCartItem, ApiResponse } from "@/api/productApi";
import {
  type ReactNode,
  useState,
  useCallback,
  useMemo,
  useEffect,
} from "react";
import { CartContext } from "./CartContext";
import { clearLocalStorage } from "@/lib/utils";
import { localStorageGuestCartIdKey } from "@/constants/constant";
import {
  useAddItemToCartMutation,
  useClearCartMutation,
  useCreateGuestCartMutation,
  useLazyGetCartQuery,
  useMergeGuestCartMutation,
  useRemoveCartMutation,
  useRemoveItemFromCartMutation,
  useSetAddressMutation,
  useSetCouponMutation,
  useSetDefaultInformationMutation,
  useSetPaymentMutation,
  useSetShippingMethodMutation,
  useUpdateQuantityMutation,
} from "@/api/cartApi";
import { usePlaceOrderMutation } from "@/api/orderApi";
import type {
  BillAddressViewModel,
  ShippingAddressViewModel,
} from "@/api/models";

export interface AddressViewModel {
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
}

export interface ShippingOption {
  name: string;
  description: string;
  price: number;
  isDefault: boolean;
}

export interface CartViewModel {
  couponMessage?: string;
  shippingCost: number;
  acct: string;
  cartId: string;
  items: CartItem[];
  shippingOptions: ShippingOption[];
  shippingOption: ShippingOption | null;
  shippingAddress: AddressViewModel | null;
  billingAddress: AddressViewModel | null;
  memberCardOnFileId: number;
  promotionalCode?: string;
  subtotal: number;
  tax: number;
  total: number;
  discount: number;
  pickupItemDescs: string;
}

const initialCartState: CartViewModel = {
  acct: "",
  cartId: "",
  items: [],
  shippingOptions: [],
  shippingOption: null,
  shippingAddress: null,
  billingAddress: null,
  memberCardOnFileId: 0,
  shippingCost: 0,
  subtotal: 0,
  tax: 0,
  total: 0,
  discount: 0,
  pickupItemDescs: "",
};

export const CartProvider = ({ children }: { children: ReactNode }) => {
  const [createGuestCart] = useCreateGuestCartMutation();
  const [triggerGetCart] = useLazyGetCartQuery();
  const [addItemToCart] = useAddItemToCartMutation();
  const [updateQuantity] = useUpdateQuantityMutation();
  const [removeItemFromCart] = useRemoveItemFromCartMutation();
  const [clearCartMutation] = useClearCartMutation();
  const [mergeGuestCart] = useMergeGuestCartMutation();
  const [setAddress] = useSetAddressMutation();
  const [setPayment] = useSetPaymentMutation();
  const [triggerSetShippingMethod] = useSetShippingMethodMutation();
  const [triggerSetCoupon] = useSetCouponMutation();
  const [triggerSetDefaultInformation] = useSetDefaultInformationMutation();
  const [triggerPlaceOrder, { isLoading: isPlacingOrder }] =
    usePlaceOrderMutation();
  const [removeCartMutation] = useRemoveCartMutation();
  const [cart, setCart] = useState<CartViewModel>(initialCartState);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (isInitialized) return; // Prevent multiple calls

    const initializeCart = async () => {
      const cartId = localStorage.getItem(localStorageGuestCartIdKey);
      try {
        if (cartId) {
          const cartData = await triggerGetCart(cartId).unwrap();
          setCart({ ...initialCartState, ...cartData });
        } else {
          const cartData = await createGuestCart().unwrap();
          localStorage.setItem(localStorageGuestCartIdKey, cartData.cartId);
          setCart({ ...initialCartState, ...cartData });
        }
      } catch (error) {
        console.error("Failed to initialize cart:", error);
      } finally {
        setIsInitialized(true);
      }
    };

    initializeCart();
  }, [createGuestCart, isInitialized, triggerGetCart]);

  // Item management functions
  const addToCart = useCallback(
    (item: AddCartItem) => {
      return addItemToCart({ item, cartId: cart?.cartId })
        .unwrap()
        .then((cartData: ApiResponse<CartViewModel>) => {
          setCart(cartData.data);
          return cartData;
        });
    },
    [addItemToCart, cart?.cartId]
  );

  const removeFromCart = useCallback(
    (itemCode: string) => {
      return removeItemFromCart({ itemCode, cartId: cart?.cartId })
        .unwrap()
        .then((cartData: ApiResponse<CartViewModel>) => {
          setCart(cartData.data);
          return cartData;
        });
    },
    [removeItemFromCart, cart?.cartId]
  );

  const updateItemQuantity = useCallback(
    (itemCode: string, quantity: number) => {
      if (quantity <= 0) {
        removeFromCart(itemCode);
        return;
      }

      updateQuantity({ itemCode, quantity, cartId: cart?.cartId })
        .unwrap()
        .then((cartData: CartViewModel) => {
          setCart(cartData);
        });
    },
    [updateQuantity, cart?.cartId, removeFromCart]
  );

  const getItemQuantity = useCallback(
    (itemCode: string) => {
      const item = cart?.items.find((item) => item.itemCode === itemCode);
      return item?.quantity ?? 0;
    },
    [cart?.items]
  );

  const increaseQuantity = useCallback(
    (itemCode: string) => {
      const prevQuantity = getItemQuantity(itemCode);
      updateItemQuantity(itemCode, prevQuantity + 1);
    },
    [getItemQuantity, updateItemQuantity]
  );

  const decreaseQuantity = useCallback(
    (itemCode: string) => {
      const prevQuantity = getItemQuantity(itemCode);
      updateItemQuantity(itemCode, prevQuantity - 1);
    },
    [getItemQuantity, updateItemQuantity]
  );

  const clearCart = useCallback(() => {
    clearCartMutation(cart?.cartId)
      .unwrap()
      .then((cartData: CartViewModel) => {
        setCart({ ...initialCartState, ...cartData });
      });
  }, [cart?.cartId, clearCartMutation]);

  const placeOrder = useCallback(
    (recaptchaToken: string) => {
      return triggerPlaceOrder(recaptchaToken)
        .unwrap()
        .then((orderData) => {
          if (orderData.success) {
            clearCart();
          }
          return orderData;
        });
    },
    [clearCart, triggerPlaceOrder]
  );

  const mergeGuestCartWithUserCart = useCallback(() => {
    if (!cart?.cartId) return;

    mergeGuestCart(cart?.cartId)
      .unwrap()
      .then((cartData: CartViewModel) => {
        setCart(cartData);
        localStorage.removeItem(localStorageGuestCartIdKey);
      });
  }, [cart?.cartId, mergeGuestCart]);

  // Cart property setters
  const setAccount = useCallback((acct: string) => {
    setCart((prevCart) => ({ ...prevCart, acct }));
  }, []);

  const setShippingMethod = useCallback(
    (method: ShippingOption) => {
      triggerSetShippingMethod({ shippingMethod: method })
        .unwrap()
        .then((cartData: CartViewModel) => {
          setCart(cartData);
        });
    },
    [triggerSetShippingMethod]
  );

  const setShippingAddress = useCallback(
    (address: ShippingAddressViewModel | null) => {
      setAddress({ shippingAddressId: address?.id })
        .unwrap()
        .then((cartData: CartViewModel) => {
          setCart(cartData);
        });
    },
    [setAddress]
  );

  const setBillingAddress = useCallback(
    (address: BillAddressViewModel | null) => {
      setAddress({ billingAddressId: address?.webstoreBillingAddressId })
        .unwrap()
        .then((cartData: CartViewModel) => {
          setCart(cartData);
        });
    },
    [setAddress]
  );

  const setMemberCardOnFile = useCallback(
    (cardId: number) => {
      setPayment({ id: cardId })
        .unwrap()
        .then((cartData: CartViewModel) => {
          setCart(cartData);
        });
    },
    [setPayment]
  );

  const setCoupon = useCallback(
    (coupon: string) => {
      return triggerSetCoupon({ coupon })
        .unwrap()
        .then((cartData: ApiResponse<CartViewModel>) => {
          setCart(cartData.data);
          return cartData;
        });
    },
    [triggerSetCoupon]
  );

  const setDefaultInformation = useCallback(
    ({
      shippingAddressId,
      billingAddressId,
      memberCardOnFileId,
    }: {
      shippingAddressId?: number;
      billingAddressId?: number;
      memberCardOnFileId?: number;
    }) => {
      triggerSetDefaultInformation({
        cartId: cart?.cartId,
        shippingAddressId,
        billingAddressId,
        memberCardOnFileId,
      })
        .unwrap()
        .then((cartData: CartViewModel) => {
          setCart(cartData);
        });
    },
    [cart?.cartId, triggerSetDefaultInformation]
  );

  const removeCart = useCallback(() => {
    return removeCartMutation()
      .unwrap()
      .then(() => {
        setCart(initialCartState);
      });
  }, [removeCartMutation]);

  // Computed values
  const getTotalItems = useCallback(() => {
    return cart?.items.reduce((total, item) => total + (item.quantity ?? 1), 0);
  }, [cart?.items]);

  const getTotalPrice = useCallback(() => {
    return cart?.items.reduce(
      (total, item) => total + (item.price ?? 0) * (item.quantity ?? 1),
      0
    );
  }, [cart?.items]);

  const isEmpty = useMemo(() => cart?.items?.length === 0, [cart?.items]);

  const isItemInCart = useCallback(
    (itemCode: string) => {
      return cart?.items.some((item) => item.itemCode === itemCode);
    },
    [cart?.items]
  );

  // Full cart operations
  const setCartData = useCallback((cartData: Partial<CartViewModel>) => {
    setCart((prevCart) => ({ ...prevCart, ...cartData }));
  }, []);

  const resetCart = useCallback(() => {
    setCart(initialCartState);
    clearLocalStorage(localStorageGuestCartIdKey);
  }, []);

  const contextValue = useMemo(
    () => ({
      // Cart state
      cart: cart?.items, // For components expecting cart to be an array of CartItem
      cartData: cart, // For components needing the full CartViewModel
      isEmpty,
      shippingCost: cart?.shippingCost ?? 0,
      subtotal: cart?.subtotal ?? 0,
      tax: cart?.tax ?? 0,
      total: cart?.total ?? 0,
      discount: cart?.discount ?? 0,
      loading: isPlacingOrder,

      // Item operations
      addToCart,
      removeFromCart,
      updateItemQuantity,
      increaseQuantity,
      decreaseQuantity,
      clearCart,
      placeOrder,
      resetCart,
      mergeGuestCartWithUserCart,

      // Cart properties
      setAccount,
      setShippingMethod,
      setShippingAddress,
      setBillingAddress,
      setMemberCardOnFile,
      setCoupon,
      setDefaultInformation,
      setCartData,
      removeCart,

      // Computed values
      getTotalItems,
      getTotalPrice,
      getItemQuantity,
      isItemInCart,
    }),
    [
      cart,
      isEmpty,
      addToCart,
      removeFromCart,
      updateItemQuantity,
      increaseQuantity,
      decreaseQuantity,
      clearCart,
      placeOrder,
      resetCart,
      mergeGuestCartWithUserCart,
      setAccount,
      setShippingMethod,
      setShippingAddress,
      setBillingAddress,
      setMemberCardOnFile,
      setCoupon,
      setDefaultInformation,
      setCartData,
      removeCart,
      getTotalItems,
      getTotalPrice,
      getItemQuantity,
      isItemInCart,
      isPlacingOrder,
    ]
  );

  return (
    <CartContext.Provider value={contextValue}>{children}</CartContext.Provider>
  );
};
