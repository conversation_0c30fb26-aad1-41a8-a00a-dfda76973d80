import { ImageOff } from "lucide-react";

const DisplayImage = ({ img }: { img: string }) => {
  if (!img)
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded">
        <ImageOff className="w-6 h-6 text-gray-400" />
      </div>
    );

  return (
    <img src={img} alt={`Preview`} className="w-full h-full object-cover" />
  );
};
export default DisplayImage;
