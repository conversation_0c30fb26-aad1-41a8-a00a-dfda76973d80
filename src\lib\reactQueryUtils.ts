import type { FetchBaseQueryError } from "@reduxjs/toolkit/query";

interface ErrorResponse {
  messages?: string;
  [key: string]: unknown;
}

export const getErrorMessage = (error: unknown): string => {
  if (!error) return "An unknown error occurred.";

  // Check if it's a FetchBaseQueryError
  if (typeof error === 'object' && 'status' in error) {
    const fetchError = error as FetchBaseQueryError;


    if (typeof fetchError.data === 'string') {
      return fetchError.data;
    }

    if (typeof fetchError.data === 'object' && fetchError.data !== null) {
      const data = fetchError.data as ErrorResponse;
      return data.messages ?? 'An unexpected error occurred.';
    }

    return `Request failed with status ${fetchError.status}`;
  }

  return 'An unknown error occurred.';
};
