import { <PERSON>, <PERSON>CheckBig, CirclePlus, CircleX } from "lucide-react";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { getLast4Digits } from "@/lib/utils";
import CreditCardDialog from "@/components/profiles/CreditCardDialog";
import { useUserProfile } from "@/hooks/useUserProfile";
import CardLogo, { type CardType } from "@/components/shared/CardLogo";
import { CustomConfirmDialog } from "@/components/common/CustomConfirmDialog";
import type { ApiError, ApiResponse } from "@/api/productApi";
import { toast } from "@/hooks/use-toast";
import { useAlertDialog } from "@/hooks/useAlertDialog";
import ChangePasswordForm from "@/components/profiles/ChangePasswordForm";
import ProfileTabProvider from "@/components/profiles/contexts/ProfileTabProvider";
import PersonalInformationTab from "@/components/profiles/tabs/PersonalInfomationTab";
import BillAddressTab from "@/components/profiles/tabs/BillAddressTab";
import ShippingAddressTab from "@/components/profiles/tabs/ShippingAddressTab";

const ProfilePage = () => {
  const [isOpenCreditCardDialog, setIsOpenCreditCardDialog] = useState(false);
  const [confirmDeleteCreditCardDialog, setConfirmDeleteCreditCardDialog] =
    useState({
      isOpen: false,
      title: "",
      message: "",
      onCancel: () => {},
      onConfirm: () => {},
    });
  const alert = useAlertDialog();
  const { userProfile, setDefaultCardOnFile, deleteCardOnFile, loading } =
    useUserProfile();
  const creditCards = userProfile?.creditCards ?? [];

  function handleSetDefault(id: number): void {
    setDefaultCardOnFile(id)
      .then((response: ApiResponse<void>) => {
        if (response.success) {
          toast({
            title: "✅ Your default card has been set.",
            variant: "default",
          });
        } else {
          alert({
            title: "Error",
            description: response?.message,
          });
        }
      })
      .catch((error: ApiError) => {
        alert({
          title: "Error",
          description: error?.data?.message ?? "Failed to set default card",
          confirmText: "OK",
          showCancel: false,
        });
      });
  }

  function handleDelete(id: number): void {
    setConfirmDeleteCreditCardDialog({
      isOpen: true,
      title: "Delete Credit Card",
      message: "Are you sure you want to delete this credit card?",
      onConfirm: () => {
        setConfirmDeleteCreditCardDialog({
          ...confirmDeleteCreditCardDialog,
          isOpen: false,
        });
        deleteCardOnFile(id)
          .then((response: ApiResponse<void>) => {
            if (response.success) {
              toast({
                title: "✅ Your credit card has been deleted.",
                variant: "default",
              });
            } else {
              alert({
                title: "Error",
                description: response?.message,
              });
            }
          })
          .catch((error: ApiError) => {
            alert({
              title: "Error",
              description:
                error?.data?.message ?? "Failed to delete credit card",
              confirmText: "OK",
              showCancel: false,
            });
          });
      },
      onCancel: () => {
        setConfirmDeleteCreditCardDialog({
          ...confirmDeleteCreditCardDialog,
          isOpen: false,
        });
      },
    });
  }

  return (
    <ProfileTabProvider>
      {isOpenCreditCardDialog && (
        <CreditCardDialog
          isOpen={isOpenCreditCardDialog}
          onClose={() => setIsOpenCreditCardDialog(false)}
          setIsOpen={setIsOpenCreditCardDialog}
        />
      )}
      <CustomConfirmDialog
        open={confirmDeleteCreditCardDialog.isOpen}
        title={confirmDeleteCreditCardDialog.title}
        description={confirmDeleteCreditCardDialog.message}
        onCancel={confirmDeleteCreditCardDialog.onCancel}
        onConfirm={confirmDeleteCreditCardDialog.onConfirm}
      />
      <div className="px-4 py-10 mx-auto max-w-7xl ">
        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="profile">Profile Information</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
          </TabsList>

          {/* Profile Information Tab */}
          <TabsContent value="profile" className="mt-6">
            <PersonalInformationTab />
          </TabsContent>

          <TabsContent value="profile" className="mt-6">
            <BillAddressTab />
          </TabsContent>

          <TabsContent value="profile" className="mt-6">
            <ShippingAddressTab />
          </TabsContent>

          <TabsContent value="profile" className="mt-6">
            {/* Payments */}
            <div className="border rounded-lg p-6 mt-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Payments</h2>
                <Button
                  variant="outline"
                  onClick={() => setIsOpenCreditCardDialog(true)}
                >
                  <CirclePlus /> Add Payment
                </Button>
              </div>
              <div className="flex flex-col items-center justify-between">
                {creditCards.map((card) => (
                  <div
                    key={card.id}
                    className="w-full flex justify-between py-4"
                  >
                    <div className="flex items-center space-x-2">
                      <CardLogo
                        cardType={
                          card?.cardType?.toLocaleLowerCase() as CardType
                        }
                      />
                      <div className="flex flex-col">
                        <div>
                          Master Card ending in{" "}
                          {getLast4Digits(card?.ccMaskedNumber)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Exp: {card?.cardExpire}
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className="flex">
                        <Button
                          disabled={loading}
                          variant="ghost"
                          size="icon"
                          onClick={() => handleSetDefault(card.id)}
                        >
                          {card?.isDefault ? (
                            <CircleCheckBig className="text-green-600" />
                          ) : (
                            <Circle className="text-muted-foreground" />
                          )}
                        </Button>

                        <Button
                          disabled={loading}
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDelete(card.id)}
                        >
                          <CircleX className="text-red-600" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security" className="mt-6">
            <ChangePasswordForm />
          </TabsContent>
        </Tabs>
      </div>
    </ProfileTabProvider>
  );
};

export default ProfilePage;
