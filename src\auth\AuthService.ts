import { User, UserManager, WebStorageStateStore } from "oidc-client";

export class AuthService {
  public userManager: UserManager;

  constructor() {
    const settings = {
      authority: import.meta.env.VITE_IDENTITY_API,
      client_id: import.meta.env.VITE_CLIENT_ID,
      redirect_uri:
        window.location.origin +
        import.meta.env.VITE_BASE_PATH +
        "auth-callback",
      monitorSession: false,
      post_logout_redirect_uri: window.location.origin,
      response_type: "code",
      scope: "openid profile references",
      userStore: new WebStorageStateStore({ store: window.localStorage }),
    };

    this.userManager = new UserManager(settings);
  }

  public getUser(): Promise<User | null> {
    return this.userManager.getUser();
  }

  public login(): Promise<void> {
    console.log("Before login", localStorage);
    return this.userManager.signinRedirect();
  }

  public logout(): Promise<void> {
    return this.userManager.signoutRedirect();
  }
}
