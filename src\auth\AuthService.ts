import { User, UserManager, WebStorageStateStore } from "oidc-client";

export class AuthService {
  public userManager: UserManager;

  constructor() {
    const settings = {
      authority: import.meta.env.VITE_IDENTITY_API,
      client_id: import.meta.env.VITE_CLIENT_ID,
      client_secret: import.meta.env.VITE_CLIENT_SECRET,
      redirect_uri:
        window.location.origin +
        import.meta.env.VITE_BASE_PATH +
        "auth-callback",
      monitorSession: false,
      post_logout_redirect_uri: window.location.origin + import.meta.env.VITE_BASE_PATH,
      response_type: "code",
      scope: "openid profile references",
      userStore: new WebStorageStateStore({ store: window.localStorage }),
    };

    this.userManager = new UserManager(settings);
  }

  public getUser(): Promise<User | null> {
    return this.userManager.getUser();
  }

  public login(): Promise<void> {
    console.log("=== OIDC Login Debug Info ===");
    console.log("UserManager settings:", this.userManager.settings);
    console.log("Before login localStorage count:", localStorage.length);

    // Clear any existing OIDC state to avoid conflicts
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('oidc.')) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => {
      console.log("Removing old OIDC state:", key);
      localStorage.removeItem(key);
    });

    return this.userManager.signinRedirect().then(() => {
      console.log("After signinRedirect call - localStorage count:", localStorage.length);
      // Log what was stored
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('oidc.')) {
          console.log(`Stored: ${key}:`, localStorage.getItem(key));
        }
      }
    }).catch((error) => {
      console.error("Error during signinRedirect:", error);
      throw error;
    });
  }

  public logout(): Promise<void> {
    return this.userManager.signoutRedirect();
  }
}
