import type { OrderPaymentViewModel } from "@/api/models/orderDetailViewModel";
import Card<PERSON>ogo, { type CardType } from "@/components/shared/CardLogo";
import { breakLongWords } from "@/lib/breakLongWords";

const PaymentInformation = ({
  payment,
  billingAddres,
}: {
  payment: OrderPaymentViewModel;
  billingAddres: string;
}) => {
  return (
    <div className="border rounded-lg p-6">
      <h2 className="text-lg font-semibold mb-4">Payment Information</h2>
      <div className="flex items-center space-x-2 mb-2">
        <CardLogo
          cardType={payment?.creditCardType?.toLocaleLowerCase() as CardType}
        />
        <span className="text-sm font-medium">
          {payment?.creditCardType} (•••• {payment?.cardNum})
        </span>
        <span className="text-xs">Exp: {payment.cardExpire}</span>
      </div>
      <p className="text-xs text-muted-foreground mb-4">
        {breakLongWords(billingAddres)}
      </p>
    </div>
  );
};
export default PaymentInformation;
