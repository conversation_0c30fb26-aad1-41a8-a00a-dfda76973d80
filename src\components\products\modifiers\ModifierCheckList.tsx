import type { ModifierChoice } from "@/api/productApi";
import { Controller, type Control, type FieldValues } from "react-hook-form";

type CheckListProps<T extends FieldValues> = {
  options: ModifierChoice[];
  modifierText: string;
  forceAnswer: boolean;
  control: Control<T>;
  name: string;
};

const ModifierCheckList = ({
  options,
  modifierText,
  forceAnswer,
  control,
  name,
}: CheckListProps<FieldValues>) => {
  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium mb-2">
        {modifierText} {forceAnswer && <span className="text-red-500">*</span>}
      </h3>

      <Controller
        name={name}
        control={control}
        render={({ field }) => {
          const choices: ModifierChoice[] = field.value ?? [];

          const onToggle = (index: number) => {
            const newChoices = [...choices];
            newChoices[index] = {
              ...newChoices[index],
              isSelected: !newChoices[index].isSelected,
            };
            field.onChange(newChoices);
          };

          return (
            <>
              {options.map((option, idx) => {
                const checked = choices[idx]?.isSelected || false;
                return (
                  <label
                    key={option.choiceValue}
                    className="flex items-center space-x-2 text-sm"
                  >
                    <input
                      type="checkbox"
                      checked={checked}
                      onChange={() => onToggle(idx)}
                      className="form-checkbox"
                    />
                    <span>{option.choiceValue}</span>
                  </label>
                );
              })}
            </>
          );
        }}
      />
    </div>
  );
};

export default ModifierCheckList;
