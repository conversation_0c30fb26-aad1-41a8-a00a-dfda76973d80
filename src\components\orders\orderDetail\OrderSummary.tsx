import { formatCurrency } from "@/lib/formatCurrency";

const OrderSummary = ({
  subTotal,
  shipping,
  tax,
  total,
}: {
  subTotal: number;
  shipping: number;
  tax: number;
  total: number;
}) => {
  return (
    <div className="border rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold mb-4">Order Summary</h2>
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Subtotal</span>
          <span>{formatCurrency(subTotal, "en-US")}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Shipping</span>
          <span>{formatCurrency(shipping, "en-US")}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Tax</span>
          <span>{formatCurrency(tax, "en-US")}</span>
        </div>
        <div className="border-t pt-2 mt-2">
          <div className="flex justify-between font-semibold">
            <span>Total</span>
            <span>{formatCurrency(total, "en-US")}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderSummary;
