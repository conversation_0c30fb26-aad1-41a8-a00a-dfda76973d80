import type { BillAddressViewModel } from "./api/models";
import type { ShippingAddressViewModel } from "./api/models/shippingAddressViewModel";

// Product types
export interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  description: string;
  fullDescription?: string;
  rating: number;
  reviewCount: number;
  colors?: string[];
  sizes?: string[];
}

export interface Category {
  id: string;
  name: string;
  image: string;
}

// Cart types
export interface CartItem {
  id: string;
  name: string;
  price: number;
  image: string;
  quantity: number;
  color: string;
  size: string;
}

export interface OrderItem {
  itemDesc: string;
  itemCode: string;
  quantity: number;
  price: number;
  thumbnailImage?: string;
  imageName: string;
  recipientEmail?: string;
  modifierMessage: string;
}

export interface Order {
  id: string;
  date: string;
  total: number;
  status: string;
  shippingAddress: string;
  trackingNumber?: string;
  itemCount: number;
  items: OrderItem[];
}

export interface OrderDetail {
  orderId: number;
  orderDate: string;
  subTotal: number;
  tax: number;
  shipping: number;
  total: number;
  shippingAddress: string;
  billingAddress: string;
  payment: OrderPayment;
  items: OrderItem[];
}

export interface OrderPayment {
  creditCardType: string;
  cardNum: string;
  cardExpire: string;
}

// User types
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  recentOrders: Order[];
}

export interface Address {
  firstName: string;
  lastName: string;
  street: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  mobilePhone: string;
  email: string;
  isDefault: boolean;
}

export type AddressFormData = Omit<Address, "isDefault" | "email">;

export interface WebstoreUserProfile {
  userAddress: {
    billingAddresses: BillAddressViewModel[];
    shippingAddresses: ShippingAddressViewModel[];
  };
  creditCards: CreditCard[];
}

export interface CreditCard {
  ccMaskedNumber: string;
  cardType: string;
  cardExpire: string;
  isDefault: boolean;
  id: number;
}

export interface WebstoreUser {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  mobile_phone: string;
  addresses: Address[];
  defaultAddressId?: string;
  recentOrders: Order[];
  isAdmin?: boolean;
}

export type UserProfile = WebstoreUserProfile &
  Pick<
    WebstoreUser,
    "id" | "first_name" | "last_name" | "email" | "mobile_phone"
  >;

export interface TokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
}
export interface VerifyUserResponse {
  email: string;
  isFoundUser: boolean;
  message: string;
}
