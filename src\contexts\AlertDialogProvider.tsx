import { CustomConfirmDialog } from "@/components/common/CustomConfirmDialog";
import { useCallback, useState } from "react";
import { type ReactNode } from "react";
import { AlertDialogContext } from "./AlertDialogContext";

type AlertDialogOptions = {
  title?: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  showCancel?: boolean;
  showConfirm?: boolean;
};

export const AlertDialogProvider = ({ children }: { children: ReactNode }) => {
  const [options, setOptions] = useState<AlertDialogOptions | null>(null);

  const openDialog = useCallback((opts: AlertDialogOptions) => {
    setOptions(opts);
  }, []);
  const closeDialog = () => setOptions(null);

  return (
    <AlertDialogContext.Provider value={openDialog}>
      {children}
      {options && (
        <CustomConfirmDialog
          open={!!options}
          title={options.title}
          description={options.description}
          confirmText={options.confirmText ?? "Confirm"}
          cancelText={options.cancelText ?? "Cancel"}
          onConfirm={() => {
            options.onConfirm?.();
            closeDialog();
          }}
          onCancel={closeDialog}
          showCancel={options.showCancel ?? true}
          showConfirm={options.showConfirm ?? true}
        />
      )}
    </AlertDialogContext.Provider>
  );
};
