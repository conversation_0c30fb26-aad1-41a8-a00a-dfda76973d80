import { baseApi } from "./baseApi";

export interface ApiError {
  status: number | "FETCH_ERROR" | "PARSING_ERROR" | "CUSTOM_ERROR";
  data?: {
    message: string | undefined;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
  cartId?: string; // optional if it can be missing
}

export type CartItem = Pick<
  WebstoreProduct,
  | "id"
  | "quantity"
  | "itemCode"
  | "packageCode"
  | "description"
  | "imageName"
  | "fullDesc"
  | "price"
  | "fullImage"
  | "thumbnailImage"
  | "taxInclusivePrice"
  | "tax"
  | "recipientName"
  | "recipientEmail"
> & {
  modifierMessage?: string;
};

export type AddCartItem = Pick<
  WebstoreProduct,
  | "quantity"
  | "itemCode"
  | "recipientName"
  | "recipientEmail"
  | "choosedGiftCardType"
> & {
  modifiers?: EditModifier[];
};

export interface WebstoreProduct {
  id: number;
  itemCode: string;
  packageCode?: string;
  description: string;
  categoryId?: number;
  taxCode: string;
  price?: number;
  taxInclusivePrice?: number;
  tax?: number;
  weight?: number;
  fullDesc: string;
  unitCost?: number;
  spePrice: string;
  isSale: boolean;
  hideOnlinePrice: boolean;
  imageName: string;
  smallImageName: string;
  salePrice?: number;
  isPackage: boolean;
  orderItem: number;
  isSeePrice: boolean;
  departmentId: number;

  // Tax fields
  tax1Percent: string;
  tax1Basic: string;
  tax2Percent: string;
  tax2Basic: string;
  tax3Percent: string;
  tax3Basic: string;

  // Online info
  onlinePrice: number;
  isShowUnitInStock: string;
  unitsInStock: string;

  // Supplier info
  companyName: string;
  isOnlineGiftCard: string;

  // Category online stock display
  showUnitInStockOnline?: number;
  showUnitInStockOnlineNumber?: number;

  // Kit/Group info
  kitGroupId?: number;

  // Coupon info
  requireCoupon: string;
  assignCouponId: string;

  // Images
  fullImage: string;
  thumbnailImage: string;

  isFreeShipping: boolean;
  isContainModifiers: boolean;
  isPickupOnly: boolean;

  quantity: number; // Optional for cart operations

  recipientName?: string | null;
  recipientEmail?: string | null;
  choosedGiftCardType?: string | null;
}

export interface WebstoreProductItemDetail
  extends Pick<
    WebstoreProduct,
    | "id"
    | "quantity"
    | "itemCode"
    | "description"
    | "imageName"
    | "fullDesc"
    | "price"
    | "fullImage"
    | "thumbnailImage"
    | "isFreeShipping"
    | "isPickupOnly"
    | "hideOnlinePrice"
  > {
  isOnlineGiftCard: boolean;
  recipientName?: string | null;
  recipientEmail?: string | null;
  modifiers?: Modifier[];
  giftCard?: GifrCard;
}

export interface GifrCard {
  isGCPhysicalTypeOnly: boolean;
  isBothOnlineGiftCard: boolean;
  isManualOnlineGCPrice: boolean;
  choosedGiftCardType: string;
}

export interface ModifierChoice {
  choiceOrder: number;
  choiceValue: string;
  choiceType: string;
  choiceColor: string;
  choiceResult: string;
  isSelected: boolean;
  choiceId: number;
  choiceItemCode: string;
}

export interface Modifier {
  modifierID: number;
  modifierOrder: number;
  modifierText: string;
  modifierType: string;
  modifierResult: string;
  forceAnswer: boolean;
  choices: ModifierChoice[];
  itemCode: string;
}

export interface EditModifier {
  modifierID: number;
  modifierResult: string;
  choices: EditModifierChoice[];
}

export interface EditModifierChoice {
  choiceResult: string;
  isSelected: boolean;
  choiceId: number;
}

export interface WebstoreProductPackageDetail
  extends Pick<
    WebstoreProduct,
    | "id"
    | "quantity"
    | "itemCode"
    | "description"
    | "imageName"
    | "fullDesc"
    | "price"
    | "fullImage"
    | "thumbnailImage"
    | "isFreeShipping"
    | "isContainModifiers"
    | "isPickupOnly"
    | "packageCode"
  > {
  packageItemsPOS: WebstoreProductItemDetail[];
  modifiers?: Modifier[];
}

export interface WebstoreProductDetail {
  item: WebstoreProductItemDetail | null;
  package: WebstoreProductPackageDetail | null;
}

const productApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getProducts: builder.query<WebstoreProduct[], number | undefined>({
      query: (departmentId) => {
        return {
          url: departmentId
            ? `api/Webstores/Products?departmentId=${departmentId}`
            : `api/Webstores/Products`,
          method: "GET",
        };
      },
      providesTags: ["Product"],
    }),
    getProductDetails: builder.query<WebstoreProductDetail, string>({
      query: (itemCode) => {
        return {
          url: `api/Webstores/Products/${itemCode}`,
          method: "GET",
        };
      },
    }),
  }),
});

export const { useGetProductsQuery, useLazyGetProductDetailsQuery } =
  productApi;
