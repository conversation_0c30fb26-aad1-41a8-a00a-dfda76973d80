import { Link } from "react-router-dom";

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { useOptions } from "@/hooks/useOptions";
import { cn } from "@/lib/utils";

const Navbar = () => {
  const { options } = useOptions();
  const departments = options?.departments || [];

  const mainDepartments = departments
    .filter((dep) => dep.parentName === "")
    .sort((a, b) => (a.order ?? 0) - (b.order ?? 0));

  const renderChildren = (children: typeof departments) => (
    <NavigationMenuContent>
      <ul className="w-[400px] p-4 md:w-[500px] lg:w-[600px]">
        {children.map((child) => (
          <li key={child.id}>
            <NavigationMenuLink asChild>
              <Link
                to={{
                  pathname: "/",
                  search: `?department=${child.name}`,
                }}
                className="block p-4 hover:bg-secondary"
              >
                {child.name}
              </Link>
            </NavigationMenuLink>
          </li>
        ))}
      </ul>
    </NavigationMenuContent>
  );

  return (
    <div className="w-full bg-secondary">
      <div className="hidden sm:flex items-center h-16 px-4 md:h-20 mx-auto max-w-7xl">
        <NavigationMenu>
          <NavigationMenuList>
            {mainDepartments.map((dep) => (
              <NavigationMenuItem key={dep.id}>
                <NavigationMenuTrigger
                  className={cn(
                    "bg-transparent font-medium pl-0 md:text-lg",
                    (dep.children?.length ?? 0) === 0 && "[&>svg]:hidden"
                  )}
                >
                  {dep.children?.length <= 0 ? (
                    <Link
                      to={{
                        pathname: "/",
                        search: `?department=${dep.name}`,
                      }}
                      className="flex items-center h-full px-4"
                    >
                      {dep.name}
                    </Link>
                  ) : (
                    <span className="flex items-center h-full pl-4">
                      {dep.name}
                    </span>
                  )}
                </NavigationMenuTrigger>
                {(dep.children?.length ?? 0) > 0 &&
                  renderChildren(dep.children)}
              </NavigationMenuItem>
            ))}
          </NavigationMenuList>
        </NavigationMenu>
      </div>
    </div>
  );
};

export default Navbar;
