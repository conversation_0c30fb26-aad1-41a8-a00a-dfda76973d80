import type { Address, CreditCard } from "@/types";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const saveLocalStorage = <T>(name: string, data: T) => {
  localStorage.setItem(name, JSON.stringify(data));
};

export const saveSessionStorage = <T>(name: string, data: T) => {
  sessionStorage.setItem(name, JSON.stringify(data));
};

export const getLocalStorage = <T>(name: string): T | null => {
  const value = localStorage.getItem(name);
  if (!value) return null;
  try {
    return JSON.parse(value);
  } catch (e) {
    console.error("Failed to parse localStorage:", e);
    return null;
  }
};

export const getSessionStorage = <T>(name: string): T | null => {
  const value = sessionStorage.getItem(name);
  if (!value) return null;
  try {
    return JSON.parse(value);
  } catch (e) {
    console.error("Failed to parse sessionStorage:", e);
    return null;
  }
};

export const clearLocalStorage = (name: string) => {
  localStorage.removeItem(name);
};

export const clearAllLocalStorage = () => localStorage.clear();

export const decodeBase64 = (encoded: string) => {
  try {
    return atob(encoded);
  } catch (e) {
    console.error("Invalid Base64 string:", e);
    return "";
  }
};

export const formatPrice = (price: number): string => {
  if (!price) return "$0.00";
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(price);
};

export const getEffectivePrice = (product: {
  price?: number;
  salePrice?: number;
  onlinePrice?: number;
}): number => {
  if (product.onlinePrice && product.onlinePrice > 0) {
    return product.onlinePrice;
  }
  if (product.salePrice && product.salePrice > 0) {
    return product.salePrice;
  }
  return product.price ?? 0;
};

export const formatCreditCard = (card: CreditCard): string => {
  const lastFourDigits = card.ccMaskedNumber.slice(-4);
  const maskedNumber = `**** ${lastFourDigits}`;
  const cardExpire = `${card.cardExpire.slice(0, 2)}/${card.cardExpire.slice(
    2
  )}`;

  return `${maskedNumber} ${card.cardType} Exp: ${cardExpire}`;
};

export const getLast4Digits = (cardNumber: string): string => {
  if (!cardNumber) return "";
  if (cardNumber.length < 4) return cardNumber;
  return cardNumber.slice(-4);
};

export const formatFullDateDetail = (date: string): string => {
  return new Date(date).toLocaleDateString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

export const formatAddress = (address: Address): string => {
  const {
    firstName = "",
    lastName = "",
    street = "",
    city = "",
    state = "",
    zip = "",
  } = address;

  return `${firstName} ${lastName} ${street} ${city}, ${state} ${zip}`.trim();
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const debounce = <T extends (...args: any[]) => void>(
  fn: T,
  delay = 500
) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
};
