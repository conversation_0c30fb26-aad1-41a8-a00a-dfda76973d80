import {
  Dialog,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import BillAddressForm from "../shared/form/BillAddressForm";
import ShippingAddressForm from "../shared/form/ShippingAddressForm";

interface AddressDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  form: "billing" | "shipping";
  addressId?: number;
}

const AddressDialog = ({
  isOpen,
  onClose,
  title,
  form,
  addressId,
}: AddressDialogProps) => {
  let formContent;
  if (form === "billing") {
    formContent = <BillAddressForm id={addressId} onCancel={onClose} />;
  } else if (form === "shipping") {
    formContent = <ShippingAddressForm id={addressId} onCancel={onClose} />;
  } else {
    formContent = <>Not support yet</>;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg" aria-description="Edit Address">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        {formContent}
      </DialogContent>
    </Dialog>
  );
};

export default AddressDialog;
