import { useMemo } from "react";
import { useUserProfile } from "@/hooks/useUserProfile";
import { useProfileTab } from "../hooks/useProfileTab";
import EditButton from "../EditButton";
import ShippingAddressForm from "../../shared/form/ShippingAddressForm";
import NoDefaultAddress from "../NoDefaultAddress";

const ShippingAddressTab = () => {
  const { userProfile } = useUserProfile();
  const { editTab, displayAddress, onEditTab } = useProfileTab();

  const defaultShippingAddress = useMemo(
    () => userProfile?.userAddress.shippingAddresses.find((i) => i.isDefault),
    [userProfile?.userAddress.shippingAddresses]
  );

  return (
    <div className="border rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold">Shipping Address</h2>
        {editTab === "none" && <EditButton mode="shipping" />}
      </div>

      {editTab === "shipping" ? (
        <ShippingAddressForm
          id={defaultShippingAddress?.id}
          onCancel={() => onEditTab("none")}
          disableSetIsDefault
        />
      ) : !defaultShippingAddress ? (
        <NoDefaultAddress form="shipping"/>
      ) : (
        <div
          className="flex items-center justify-between mb-6"
          key={`ship-${defaultShippingAddress.id}`}
        >
          <div>
            {displayAddress(defaultShippingAddress)}

            {defaultShippingAddress.isDefault && (
              <span className="ml-2 inline-block rounded-full bg-blue-500 text-white text-xs px-2 py-0.5 font-semibold">
                Default
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ShippingAddressTab;
