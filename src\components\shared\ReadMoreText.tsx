import { useState } from "react";
import { But<PERSON> } from "../ui/button";

interface ReadMoreProps {
  text: string;
  maxLength?: number;
}

const ReadMore = ({ text = "", maxLength = 100 }: ReadMoreProps) => {
  const [expanded, setExpanded] = useState(false);

  if (text.length <= maxLength) {
    return <span>{text}</span>;
  }

  const toggleExpanded = () => setExpanded(!expanded);

  return (
    <span>
      {expanded ? text : `${text.substring(0, maxLength)}... `}
      <Button
        onClick={toggleExpanded}
        type="button"
        variant="link"
        className="px-0 py-0 text-blue-600 h-auto ml-2 font-medium hover:underline"
      >
        {expanded ? "Show less" : "Read more"}
      </Button>
    </span>
  );
};

export default ReadMore;
