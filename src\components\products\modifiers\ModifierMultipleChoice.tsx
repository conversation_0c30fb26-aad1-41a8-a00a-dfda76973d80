import type { ModifierChoice } from "@/api/productApi";
import { Controller, type Control, type FieldValues } from "react-hook-form";

type MultipleChoiceProps<T extends FieldValues> = {
  options: ModifierChoice[];
  modifierText: string;
  forceAnswer: boolean;
  control: Control<T>;
  name: string;
};

const ModifierMultipleChoice = ({
  options,
  modifierText,
  forceAnswer,
  control,
  name,
}: MultipleChoiceProps<FieldValues>) => {
  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium mb-2">
        {modifierText}{" "}
        {forceAnswer ? <span className="text-red-500">*</span> : null}
      </h3>

      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <>
            {options.map((option) => (
              <label
                key={option.choiceValue}
                className="flex items-center space-x-2 text-sm"
              >
                <input
                  type="radio"
                  value={option.choiceValue}
                  checked={field.value === option.choiceValue}
                  onChange={() => field.onChange(option.choiceValue)}
                  name={field.name}
                  className="form-radio"
                />
                <span>{option.choiceValue}</span>
              </label>
            ))}
          </>
        )}
      />
    </div>
  );
};

export default ModifierMultipleChoice;
