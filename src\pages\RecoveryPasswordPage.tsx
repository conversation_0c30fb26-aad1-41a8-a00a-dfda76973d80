import { But<PERSON> } from "@/components/ui/button";
import logo from "../assets/logo.png";
import { Link, useParams } from "react-router-dom";
import { useCallback, useEffect, useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { DialogTitle } from "@radix-ui/react-dialog";
import { useForgotPasswordMutation } from "@/api/authApi";
import LoadingOverlay from "@/components/shared/LoadingOverlay";

const RecoveryPasswordPage = () => {
  const { email } = useParams();
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [forgotPassword, { isLoading, isSuccess, isError, error }] =
    useForgotPasswordMutation();

  const onSendPasswordRecovery = useCallback(() => {
    if (!email) throw Error("send password recovery need email");
    forgotPassword({
      email,
      url: `${window.location.origin}/reset-password`,
    });
  }, [email, forgotPassword]);

  useEffect(() => {
    setOpenDialog(isSuccess || isError);
  }, [isError, isSuccess]);

  return (
    <main className="flex flex-1 min-h-[600px]">
      <aside className="flex justify-center items-center bg-gray-100 w-[600px] max-md:w-6/12 max-sm:hidden">
        <img src={logo} alt="Club Prophet Logo" className="h-auto w-80" />
      </aside>

      {isLoading && <LoadingOverlay />}

      <Dialog open={openDialog} onOpenChange={setOpenDialog}>
        <DialogContent>
          <DialogTitle>Password Recovery</DialogTitle>
          {error
            ? "Error"
            : "Your Login information will be sent via email in a few minutes. Please check your email for login instructions and try again."}
        </DialogContent>
      </Dialog>

      <section className="flex flex-1 justify-center pt-24">
        <div className="flex flex-col items-center w-[399px]">
          <h2 className="mb-6 text-2xl font-bold  w-3/4">Password Recovery</h2>
          <form className="w-3/4">
            <div className="space-y-4">
              <div>
                <label
                  className="block text-sm font-medium mb-1"
                  htmlFor="email"
                >
                  Email
                </label>
                <input
                  autoComplete="email"
                  type="email"
                  id="email"
                  className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                  value={email}
                  readOnly
                />
              </div>
            </div>
            <Button
              className="mt-6 w-full"
              onClick={(e) => {
                e.preventDefault();
                onSendPasswordRecovery();
              }}
            >
              Send Password Recovery
            </Button>
            <Link to="/login">
              <Button variant="outline" className="mt-2 w-full">
                Back
              </Button>
            </Link>
          </form>
        </div>
      </section>
    </main>
  );
};

export default RecoveryPasswordPage;
