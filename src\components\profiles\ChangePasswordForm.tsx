import { useForm, useWatch, type SubmitHand<PERSON> } from "react-hook-form";
import type { WebstoreChangePasswordViewModel } from "@/api/models/webstoreChangePasswordViewModel";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import useValidationSchema from "@/hooks/useValidationSchema";
import { useChangePasswordMutation } from "@/api/userApi";
import FieldInput from "../input/FieldInput";
import PasswordStrengthBar from "../shared/PasswordStrengthBar";
import { useCallback } from "react";
import CustomButton from "../common/CustomButton";
import { getErrorMessage } from "@/lib/reactQueryUtils";
import { toast } from "@/hooks/use-toast";

type FormType = WebstoreChangePasswordViewModel;

const ChangePasswordForm = () => {
  const { passwordSchema, confirmPasswordSchema, validateConfirmMatch } =
    useValidationSchema();

  const [changePassword, { isLoading, error }] = useChangePasswordMutation();

  const form = useForm<FormType>({
    mode: "onChange",
    defaultValues: {
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
    resolver: zodResolver(
      z
        .object({
          oldPassword: z.string().min(1, "Current Password is required."),
          newPassword: passwordSchema,
          confirmPassword: confirmPasswordSchema,
        })
        .superRefine((values, ctx) => {
          validateConfirmMatch(values.newPassword, values.confirmPassword, ctx);
        })
    ),
  });

  const {
    control,
    handleSubmit,
    formState: { isDirty, isValid },
  } = form;
  const newPassword = useWatch({
    control,
    name: "newPassword",
  });

  const onSubmit = useCallback<SubmitHandler<WebstoreChangePasswordViewModel>>(
    async (formData: FormType) => {
      await changePassword(formData).unwrap();
      toast({
        title: "✅ Change Password Successfully.",
        variant: "default",
      });
    },
    [changePassword]
  );

  return (
    <div className="border rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold mb-6">Change Password</h2>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-4">
          <div>
            <FieldInput<FormType>
              name="oldPassword"
              control={control}
              label="Current Password"
            />
          </div>
          <div>
            <FieldInput<FormType>
              name="newPassword"
              control={control}
              label="New Password"
            />
            <PasswordStrengthBar password={newPassword} />
          </div>
          <div>
            <FieldInput<FormType>
              name="confirmPassword"
              control={control}
              label="Confirm New Password"
            />
          </div>
        </div>
        <CustomButton
          type="submit"
          variant="default"
          isLoading={isLoading}
          disabled={!isDirty || !isValid}
          className="mt-6 w-[10rem]"
        >
          Update Password
        </CustomButton>
        {error && (
          <p className="text-red-500 text-xs mt-2">{getErrorMessage(error)}</p>
        )}
      </form>
    </div>
  );
};

export default ChangePasswordForm;
