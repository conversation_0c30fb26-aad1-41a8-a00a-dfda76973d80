import type { CartViewModel, ShippingOption } from "@/contexts/CartProvider";
import { baseApi } from "./baseApi";
import type { AddCartItem, ApiResponse } from "./productApi";

const cartApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getCart: builder.query<CartViewModel, string | undefined | null>({
      query: (cartId: string | undefined | null) => ({
        url: cartId
          ? `api/Webstores/Carts?cartId=${cartId}`
          : `api/Webstores/Carts`,
        method: "GET",
      }),
      transformResponse: (response: ApiResponse<CartViewModel>) => {
        return response.data;
      },
    }),
    createGuestCart: builder.mutation<CartViewModel, void>({
      query: () => ({
        url: `api/Webstores/Carts/Guest`,
        method: "POST",
      }),
    }),
    addItemToCart: builder.mutation<
      ApiResponse<CartViewModel>,
      { item: AddCartItem; cartId: string }
    >({
      query: ({ item, cartId }) => ({
        url: cartId
          ? `api/Webstores/Carts/Add?cartId=${cartId}`
          : `api/Webstores/Carts/Add`,
        method: "POST",
        body: item,
      }),
    }),
    updateQuantity: builder.mutation<
      CartViewModel,
      { itemCode: string; quantity: number; cartId: string }
    >({
      query: ({ itemCode, quantity, cartId }) => ({
        url: cartId
          ? `api/Webstores/Carts/Update?cartId=${cartId}`
          : `api/Webstores/Carts/Update`,
        method: "PUT",
        body: { itemCode, quantity },
      }),
      transformResponse: (response: ApiResponse<CartViewModel>) => {
        return response.data;
      },
    }),
    removeItemFromCart: builder.mutation<
      ApiResponse<CartViewModel>,
      { itemCode: string; cartId: string }
    >({
      query: ({ itemCode, cartId }) => ({
        url: cartId
          ? `api/Webstores/Carts/Remove/${itemCode}?cartId=${cartId}`
          : `api/Webstores/Carts/Remove/${itemCode}`,
        method: "DELETE",
      }),
    }),
    clearCart: builder.mutation<CartViewModel, string | undefined | null>({
      query: (cartId: string | undefined | null) => ({
        url: cartId
          ? `api/Webstores/Carts/Clear?cartId=${cartId}`
          : `api/Webstores/Carts/Clear`,
        method: "DELETE",
      }),
      transformResponse: (response: ApiResponse<CartViewModel>) => {
        return response.data;
      },
    }),
    mergeGuestCart: builder.mutation<CartViewModel, string>({
      query: (cartId) => ({
        url: `api/Webstores/Carts/Merge`,
        method: "POST",
        body: { guestCartId: cartId },
      }),
      transformResponse: (response: ApiResponse<CartViewModel>) => {
        return response.data;
      },
    }),
    setAddress: builder.mutation<
      CartViewModel,
      {
        billingAddressId?: number;
        shippingAddressId?: number;
      }
    >({
      query: ({ billingAddressId, shippingAddressId }) => ({
        url: `api/Webstores/Carts/SetAddress`,
        method: "POST",
        body: { billingAddressId, shippingAddressId },
      }),
      transformResponse: (response: ApiResponse<CartViewModel>) => {
        return response.data;
      },
    }),
    setPayment: builder.mutation<
      CartViewModel,
      {
        id?: number;
      }
    >({
      query: ({ id }) => ({
        url: `api/Webstores/Carts/SetPayment`,
        method: "POST",
        body: { id },
      }),
      transformResponse: (response: ApiResponse<CartViewModel>) => {
        return response.data;
      },
    }),
    setShippingMethod: builder.mutation<
      CartViewModel,
      {
        shippingMethod: ShippingOption;
      }
    >({
      query: ({ shippingMethod }) => ({
        url: `api/Webstores/Carts/SetShippingMethod`,
        method: "POST",
        body: { shippingMethod },
      }),
      transformResponse: (response: ApiResponse<CartViewModel>) => {
        return response.data;
      },
    }),
    setCoupon: builder.mutation<
      ApiResponse<CartViewModel>,
      {
        coupon: string;
      }
    >({
      query: ({ coupon }) => ({
        url: `api/Webstores/Carts/SetCoupon`,
        method: "POST",
        body: { couponCode: coupon },
      }),
    }),
    setDefaultInformation: builder.mutation<
      CartViewModel,
      {
        cartId: string;
        shippingAddressId?: number;
        billingAddressId?: number;
        memberCardOnFileId?: number;
      }
    >({
      query: ({
        cartId,
        shippingAddressId,
        billingAddressId,
        memberCardOnFileId,
      }) => ({
        url: `api/Webstores/Carts/SetDefaultInformation`,
        method: "POST",
        body: {
          cartId,
          shippingAddressId,
          billingAddressId,
          memberCardOnFileId,
        },
      }),
      transformResponse: (response: ApiResponse<CartViewModel>) => {
        return response.data;
      },
    }),
    removeCart: builder.mutation<void, void>({
      query: () => ({
        url: `api/Webstores/Carts/Remove`,
        method: "DELETE",
      }),
    }),
  }),
});

export const {
  useLazyGetCartQuery,
  useCreateGuestCartMutation,
  useAddItemToCartMutation,
  useUpdateQuantityMutation,
  useRemoveItemFromCartMutation,
  useClearCartMutation,
  useMergeGuestCartMutation,
  useSetAddressMutation,
  useSetPaymentMutation,
  useSetShippingMethodMutation,
  useSetCouponMutation,
  useSetDefaultInformationMutation,
  useRemoveCartMutation,
} = cartApi;
