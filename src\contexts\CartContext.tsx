import type { AddCartItem, ApiResponse, CartItem } from "@/api/productApi";
import type { OrderDetail } from "@/types";
import { createContext } from "react";
import type { CartViewModel, ShippingOption } from "./CartProvider";
import type {
  BillAddressViewModel,
  ShippingAddressViewModel,
} from "@/api/models";

export interface CartContextType {
  cart: CartItem[];
  shippingCost: number;
  addToCart: (item: AddCartItem) => Promise<ApiResponse<CartViewModel>>;
  removeFromCart: (itemCode: string) => Promise<ApiResponse<CartViewModel>>;
  clearCart: () => void;
  placeOrder: (recaptchaToken: string) => Promise<ApiResponse<OrderDetail>>;
  mergeGuestCartWithUserCart: () => void;
  getTotalItems: () => number;
  getTotalPrice: () => number;
  increaseQuantity: (itemCode: string) => void;
  decreaseQuantity: (itemCode: string) => void;
  isEmpty: boolean;
  setShippingMethod: (method: ShippingOption) => void;
  setShippingAddress: (address: ShippingAddressViewModel) => void;
  setBillingAddress: (address: BillAddressViewModel) => void;
  setMemberCardOnFile: (cardId: number) => void;
  setCoupon: (coupon: string) => Promise<ApiResponse<CartViewModel>>;
  setDefaultInformation: ({
    shippingMethod,
    shippingAddressId,
    billingAddressId,
    memberCardOnFileId,
  }: {
    shippingMethod?: number;
    shippingAddressId?: number;
    billingAddressId?: number;
    memberCardOnFileId?: number;
  }) => void;
  removeCart: () => Promise<void>;
  loading: boolean;
  subtotal: number;
  tax: number;
  total: number;
  discount: number;
  cartData: CartViewModel;
}

export const CartContext = createContext<CartContextType | undefined>(
  undefined
);
