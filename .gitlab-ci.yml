image: docker:stable
services:
  - docker:dind
stages:
  - test

sast:
  variables:
    SEARCH_MAX_DEPTH: "20"
    SAST_EXCLUDED_PATHS: public,obj,bin,node_modules,**/.node-version,**/.editorconfig,**/.dockerignore,**/.gitignore
  stage: test
include:
  - template: Security/SAST.gitlab-ci.yml
  - template: Security/Dependency-Scanning.gitlab-ci.yml
  - template: Security/Secret-Detection.gitlab-ci.yml
