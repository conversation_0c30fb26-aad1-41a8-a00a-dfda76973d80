import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { dateFormat } from "@/lib/dateFormat";
import OrderDetail from "@/components/orders/orderDetail/OrderDetail";
import OrderSummary from "@/components/orders/orderDetail/OrderSummary";
import OrderShipping from "@/components/orders/orderDetail/OrderShipping";
import PaymentInformation from "@/components/orders/orderDetail/PaymentInformation";
import { useGetOrderDetailQuery } from "@/api/orderApi";
import { useMemo } from "react";
import LoadingOverlay from "@/components/shared/LoadingOverlay";

const OrderDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const { data, isLoading } = useGetOrderDetailQuery(Number(id));
  const order = useMemo(() => data?.data, [data?.data]);

  if (isLoading) return <LoadingOverlay />;

  if (!order) {
    return (
      <div className="px-4 py-20 mx-auto max-w-7xl text-center">
        <h1 className="text-3xl font-semibold mb-4">Order not found</h1>
        <p className="text-muted-foreground mb-6">
          We couldn’t find the order you were looking for.
        </p>
        <Link to="/orders">
          <Button>View All Orders</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="px-4 py-10 mx-auto max-w-7xl">
      {/* Back Button */}
      <div className="mb-8">
        <Button variant="ghost" className="pl-0" asChild>
          <Link to="/orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Orders
          </Link>
        </Button>
      </div>

      {/* Page Header */}
      <div className="mb-10">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Order #{order.orderId}
            </h1>
            <p className="text-muted-foreground text-sm">
              Placed on {dateFormat(order.orderDate, "MMM d, yyyy")}
            </p>
          </div>

          {/* Status Badge */}
          <div>
            <span
              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ring-1 ring-inset ${
                order.orderStatus === "Delivered"
                  ? "bg-green-50 text-green-700 ring-green-600/20"
                  : order.orderStatus === "Shipped"
                  ? "bg-blue-50 text-blue-700 ring-blue-600/20"
                  : "bg-yellow-50 text-yellow-700 ring-yellow-600/20"
              }`}
            >
              {order.orderStatus}
            </span>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Left: Items */}
        <div className="md:col-span-2 space-y-6">
          <OrderDetail items={order.items ?? []} />
        </div>

        {/* Right: Summary, Shipping, Payment */}
        <div className="md:col-span-1 space-y-6">
          <OrderSummary
            subTotal={order.subTotal ?? 0}
            shipping={order.shipping ?? 0}
            tax={order.tax ?? 0}
            total={order.total ?? 0}
          />
          <OrderShipping billingAddress={order.billingAddress} />
          {order.payment && (
            <PaymentInformation
              payment={order.payment}
              billingAddres={order.billingAddress}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderDetailPage;