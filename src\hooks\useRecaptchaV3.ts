import { useCallback, useEffect, useState } from "react";

const useRecaptchaV3 = ({
  enable,
  recaptchaSiteKey,
}: {
  enable: boolean;
  recaptchaSiteKey: string;
}) => {
  const [isReady, setIsReady] = useState<boolean>(false);
  const [isAvailable, setIsAvailable] = useState<boolean>(true);

  const scriptUrl = `https://www.google.com/recaptcha/enterprise.js?render=${recaptchaSiteKey}`;

  const getRecaptchaToken = useCallback(
    async (action: "login" | "register" | "checkout" = "login") => {
      try {
        if (!window.grecaptcha?.enterprise) {
          setIsAvailable(false);
          return;
        }
        return window.grecaptcha?.enterprise
          ?.execute(recaptchaSiteKey, {
            action,
          })
          .then((recaptchaToken: string) => {
            return recaptchaToken;
          });
      } catch (error) {
        console.error("❌ Failed to get reCAPTCHA token:", error);
      }
    },
    [recaptchaSiteKey]
  );

  const clearRecaptcha = useCallback(() => {
    const badge = document.querySelector(".grecaptcha-badge");
    badge?.remove();
    setIsReady(false);
    setIsAvailable(true);
  }, []);

  const initRecaptcha = useCallback(() => {
    const badge = document.querySelector(".grecaptcha-badge");
    if (!badge) {
      const script = document.createElement("script");
      script.src = scriptUrl;
      script.async = true;
      script.defer = true;

      script.onload = () => {
        window.grecaptcha?.enterprise?.ready?.(() => setIsReady(true));
      };
      document.head.appendChild(script);
    } else if (window.grecaptcha?.enterprise) {
      window.grecaptcha?.enterprise?.ready?.(() => setIsReady(true));
    }
  }, [scriptUrl]);

  useEffect(() => {
    if (!enable || !recaptchaSiteKey) return;
    initRecaptcha();

    return () => clearRecaptcha();
  }, [clearRecaptcha, enable, initRecaptcha, recaptchaSiteKey, scriptUrl]);

  return {
    isReady,
    isAvailable,
    getRecaptchaToken,
    clearRecaptcha,
  };
};
export default useRecaptchaV3;
