/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_KEY: string;
  readonly VITE_CLIENT_ID: string;
  readonly VITE_APIVITE_CLIENT_SECRET_URL: string;
  readonly VITE_IDENTITY_API: string;
  readonly VITE_REFERENCES_API: string;
  readonly VITE_X_COMPONENT_ID: string;
  readonly VITE_X_MODULE_ID: string;
  readonly VITE_WHITELISTED_URLS: string;
  readonly VITE_REDIRECT_URL: string;
  readonly VITE_SITE_NAME: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
