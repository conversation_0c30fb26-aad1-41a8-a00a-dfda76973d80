import { baseApi } from "./baseApi";
import type {
  WebstoreForgotPasswordViewModel,
  WebstoreResetPasswordViewModel,
  ResultViewModel,
  WebstoreMemberResultViewModel,
  WebstoreMemberViewModel,
} from "./models";

const authApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    register: builder.mutation<
      WebstoreMemberResultViewModel,
      WebstoreMemberViewModel
    >({
      query: (body: WebstoreMemberViewModel) => {
        return {
          url: "api/Webstores/Register",
          method: "POST",
          body : {
            ...body,
            siteNamespace: import.meta.env.VITE_SITE_NAME
          }
        };
      },
    }),
    forgotPassword: builder.mutation<boolean, Omit<WebstoreForgotPasswordViewModel, 'siteNamespace'>>({
      query: (body: Omit<WebstoreForgotPasswordViewModel, 'siteNamespace'>) => ({
        url: "api/Webstores/ForgotPassword",
        method: "POST",
        body : {
          ...body,
          siteNamespace: import.meta.env.VITE_SITE_NAME
        },
      }),
    }),
    isResetPasswordExpired: builder.mutation<ResultViewModel, { email: string, token: string }>({
      query: ({ email, token }) => ({
        url: `api/Webstores/IsResetPasswordExpired?email=${encodeURIComponent(email)}&token=${encodeURIComponent(token)}`,
        method: "POST"
      }),
    }),
    resetPassword: builder.mutation<ResultViewModel, WebstoreResetPasswordViewModel>({
      query: (body: WebstoreResetPasswordViewModel) => ({
        url: "api/Webstores/ResetPassword",
        method: "POST",
        body
      }),
    }),
  }),
});

export const {
  useRegisterMutation,
  useForgotPasswordMutation,
  useIsResetPasswordExpiredMutation,
  useResetPasswordMutation,
} = authApi;
