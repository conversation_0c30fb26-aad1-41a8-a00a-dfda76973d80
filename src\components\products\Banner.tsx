import { useNavigate } from "react-router-dom";
import type { WebStoreBanner } from "@/api/optionApi";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { cx } from "class-variance-authority";

interface BannersProps {
  banners: WebStoreBanner[];
}

const Banner = ({ banners }: BannersProps) => {
  const navigate = useNavigate();
  const onClickBanner = (banner: WebStoreBanner) => {
    if (banner.customLink) {
      window.open(banner.customLink, "_blank");
    } else if (banner.linkTypeDescription && banner.linkType === 1) {
      navigate({
        pathname: "/",
        search: `?department=${encodeURIComponent(banner.departmentName)}`,
      });
    }
  };

  if (!banners?.length) return null;

  return (
    <div className="flex justify-center py-8 mb-4">
      <Carousel className="w-full max-w-2xl">
        <CarouselContent>
          {banners?.map((banner) => (
            <CarouselItem key={banner.webStoreBannerId}>
              <Card
                className={cx(
                  "w-full h-full border-none",
                  (banner.linkTypeDescription && banner.linkType === 1) ||
                    banner.customLink
                    ? "cursor-pointer"
                    : "cursor-default"
                )}
                onClick={() => onClickBanner(banner)}
              >
                <CardContent className="p-0">
                  <img
                    src={banner.image}
                    alt="Banner"
                    className="w-full h-[400px] object-cover"
                  />
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious />
        <CarouselNext />
      </Carousel>
    </div>
  );
};

export default Banner;
