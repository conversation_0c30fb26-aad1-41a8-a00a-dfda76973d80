interface PickupItemNoteProps {
  pickupItemDescs: string;
}

const PickupItemNote = ({ pickupItemDescs }: PickupItemNoteProps) => {
  return (
    <div className="space-y-2 p-6 border rounded-lg bg-muted">
      <div className="text-xl font-semibold">Pickup Item: {pickupItemDescs}</div>
      <div className="text-sm text-muted-foreground">
        Note: You have pickup item(s) in your cart. Other items in this order
        can be picked up at the same time.
      </div>
    </div>
  );
};

export default PickupItemNote;
