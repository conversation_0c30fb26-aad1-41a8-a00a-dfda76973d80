import DOMPurify from "dompurify";

export function SafeIframe({ url }: { url: string | null }) {
  if (!url) return null;

  const safeUrl = DOMPurify.sanitize(url);
  const allowedDomainSuffix = ".cps.golf";
  const isAllowed =
    safeUrl.startsWith("https://") &&
    new URL(safeUrl).hostname.endsWith(allowedDomainSuffix);

  return isAllowed ? (
    <iframe
      title="SafeIframe"
      src={safeUrl}
      className="w-full h-full border-0"
      sandbox="allow-scripts allow-same-origin allow-forms"
    />
  ) : (
    <div>Invalid or unsafe URL</div>
  );
}
