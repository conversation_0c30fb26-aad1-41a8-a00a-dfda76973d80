import visa from "../../assets/cards/visa.png";
import amex from "../../assets/cards/amex.png";
import mastercard from "../../assets/cards/mastercard.png";
import discover from "../../assets/cards/discover.png";

export type CardType = "visa" | "amex" | "mastercard" | "discover";

interface CardLogoProps {
  cardType: CardType;
}

const CardLogo = ({ cardType }: CardLogoProps) => {
  const cardLogo: Record<CardType, string> = {
    visa,
    amex,
    mastercard,
    discover,
  };

  const logo = cardLogo[cardType];

  return (
    <img src={logo} alt={cardType} className="h-6 w-8 inline-block mr-2" />
  );
};

export default CardLogo;
