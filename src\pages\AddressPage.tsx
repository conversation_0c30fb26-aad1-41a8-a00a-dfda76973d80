import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import BillingAddressTab from "@/components/address/tabs/BillingAddressTab";
import ShippingAddressTab from "@/components/address/tabs/ShippingAddressTab";

const AddressPage = () => {
  return (
    <div className="px-4 py-10 mx-auto max-w-7xl ">
      <div>
        <h2 className="text-2xl font-bold sm:text-3xl">My Addresses</h2>
      </div>

      <Tabs defaultValue="billing" className="w-full mt-8">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="billing">Billing Address</TabsTrigger>
          <TabsTrigger value="shipping">Shipping Address</TabsTrigger>
        </TabsList>

        <TabsContent value="billing" className="mt-6">
          <BillingAddressTab />
        </TabsContent>

        <TabsContent value="shipping" className="mt-6">
          <ShippingAddressTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AddressPage;
