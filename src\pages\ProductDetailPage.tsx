import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Truck, ShoppingCart, ArrowLeft, Box, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useProducts } from "@/hooks/useProducts";
import type {
  WebstoreProductItemDetail,
  WebstoreProductPackageDetail,
} from "@/api/productApi";
import ReadMore from "@/components/shared/ReadMoreText";
import { useCart } from "@/hooks/useCart";
import { formatPrice, getEffectivePrice } from "@/lib/utils";
import { z } from "zod";
import { useFieldArray, useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ModifierDropdown } from "@/components/products/modifiers/ModifierDropdown";
import ModifierCheckList from "@/components/products/modifiers/ModifierCheckList";
import ModifierMultipleChoice from "@/components/products/modifiers/ModifierMultipleChoice";
import { ModifierType } from "@/types/enum";
import ModifierText from "@/components/products/modifiers/ModifierText";
import { toast } from "@/hooks/use-toast";

const schema = z.object({
  recipientName: z.string().min(1, "Recipient name is required").optional(),
  recipientEmail: z
    .string()
    .email("Invalid email")
    .min(1, "Email is required")
    .optional(),
  choosedGiftCardType: z.enum(["physical", "virtual"]).optional(),
  modifiers: z.array(
    z.object({
      modifierID: z.number(),
      modifierResult: z.string(),
      choices: z.array(
        z.object({
          choiceResult: z.string(),
          isSelected: z.boolean(),
          choiceId: z.number(),
          choiceValue: z.string(),
          choiceItemCode: z.string().optional(),
        })
      ),
    })
  ),
});

type FormType = z.infer<typeof schema>;

const ProductDetailPage = () => {
  const [productItem, setProductItem] =
    useState<WebstoreProductItemDetail | null>(null);
  const [productPackage, setProductPackage] =
    useState<WebstoreProductPackageDetail | null>(null);
  const [quantity, setQuantity] = useState(1);
  const { getSelectedProduct, loading } = useProducts();
  const { addToCart } = useCart();
  const { id } = useParams();
  const navigate = useNavigate();
  const {
    unregister,
    register,
    reset,
    handleSubmit,
    setValue,
    control,
    formState: { errors },
  } = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      recipientName: "",
      recipientEmail: "",
      choosedGiftCardType: "physical",
      modifiers: [],
    },
  });
  const { fields, append } = useFieldArray({
    control,
    name: "modifiers",
  });
  const choosedGiftCardType = useWatch({
    name: "choosedGiftCardType",
    control,
  });

  useEffect(() => {
    if (choosedGiftCardType !== "virtual") {
      unregister("recipientEmail");
    }
  }, [choosedGiftCardType, unregister]);

  useEffect(() => {
    const fetchProduct = async () => {
      setProductItem(null);
      setProductPackage(null);

      if (!id) return;
      const product = await getSelectedProduct(id);

      if (product?.item) {
        setProductItem(product?.item ?? null);
        setProductPackage(null);

        const isRequireRecipientName = product?.item?.isOnlineGiftCard ?? false;

        const isRequireRecientEmail =
          (product?.item?.isOnlineGiftCard &&
            !product?.item?.giftCard?.isGCPhysicalTypeOnly) ??
          false;

        const isBothOnlineGiftCard =
          product?.item?.giftCard?.isBothOnlineGiftCard ?? false;

        if (!isRequireRecipientName) {
          unregister("recipientName");
        }

        if (!isRequireRecientEmail) {
          unregister("recipientEmail");
        }

        if (!isBothOnlineGiftCard) {
          unregister("choosedGiftCardType");
        }

        reset({
          modifiers: [],
          choosedGiftCardType: isBothOnlineGiftCard ? "physical" : undefined,
        });
        if (product?.item?.modifiers) {
          append(
            product?.item?.modifiers.map((modifier) => ({
              modifierID: modifier.modifierID,
              modifierResult: "",
              forceAnswer: modifier.forceAnswer,
              choices: modifier.choices.map((modifier) => ({
                choiceResult: "",
                choiceId: modifier.choiceId,
                isSelected: false,
                choiceValue: modifier.choiceValue,
                choiceItemCode: modifier.choiceItemCode,
              })),
            }))
          );
        }
      } else if (product?.package) {
        unregister("recipientName");
        unregister("recipientEmail");
        setProductPackage(product?.package ?? null);
        setProductItem(null);

        const modifiers = product?.package?.packageItemsPOS?.flatMap(
          (item) => item.modifiers ?? []
        );

        reset({ modifiers: [] });
        if (modifiers) {
          append(
            modifiers.map((modifier) => ({
              modifierID: modifier.modifierID,
              modifierResult: "",
              forceAnswer: modifier.forceAnswer,
              choices: modifier.choices.map((modifier) => ({
                choiceResult: "",
                choiceId: modifier.choiceId,
                isSelected: false,
                choiceValue: modifier.choiceValue,
                choiceItemCode: modifier.choiceItemCode,
              })),
            }))
          );
        }
      }
    };

    fetchProduct();
  }, [append, getSelectedProduct, id, unregister, reset, setValue]);

  const isPackage = !!productPackage;
  const product = productItem ?? productPackage;

  const isOnlineGiftCard = productItem?.isOnlineGiftCard ?? false;
  const isBothOnlineGiftCard =
    productItem?.giftCard?.isBothOnlineGiftCard ?? false;
  const isGCPhysicalTypeOnly =
    (productItem?.giftCard?.isGCPhysicalTypeOnly ||
      choosedGiftCardType === "physical") ??
    false;

  if (loading || (!productItem && !productPackage)) {
    return (
      <div className="container py-20 text-center">Loading product...</div>
    );
  }

  if (!productItem && !productPackage) {
    return (
      <div className="container py-20 text-center text-red-500">
        Product not found
      </div>
    );
  }

  const handleAddToCart = (data: z.infer<typeof schema>) => {
    if (productItem) {
      addToCart({
        itemCode: productItem.itemCode,
        quantity,
        ...data,
        choosedGiftCardType:
          data?.choosedGiftCardType ??
          productItem?.giftCard?.choosedGiftCardType,
      })
        .then((response) => {
          if (response?.success) {
            navigate("/cart");
          } else {
            toast({
              title: "❌ Add to cart failed",
              description: response?.message,
              variant: "destructive",
            });
          }
        })
        .catch((error) => {
          toast({
            title: "❌ Add to cart failed",
            description: error?.data?.message ?? "Failed to add to cart",
            variant: "destructive",
          });
        });
    } else if (productPackage) {
      addToCart({
        itemCode: productPackage.packageCode!,
        quantity,
        modifiers: data.modifiers,
      })
        .then((response) => {
          if (response?.success) {
            navigate("/cart");
          } else {
            toast({
              title: "❌ Add to cart failed",
              description: response?.message,
              variant: "destructive",
            });
          }
        })
        .catch((error) => {
          toast({
            title: "❌ Add to cart failed",
            description: error?.data?.message ?? "Failed to add to cart",
            variant: "destructive",
          });
        });
    }
  };

  const modifiers = productItem?.modifiers ?? [];
  const modifiersPackageWithIndex = productPackage?.packageItemsPOS
    .flatMap(
      (item) =>
        item.modifiers?.map((modifier) => ({
          modifierID: modifier.modifierID,
        })) || []
    )
    .map((modifier, globalIndex) => ({
      ...modifier,
      globalIndex,
    }));

  const incrementQuantity = () => setQuantity((prev) => prev + 1);
  const decrementQuantity = () =>
    setQuantity((prev) => (prev > 1 ? prev - 1 : 1));

  return (
    <div className="px-4 py-10 mx-auto max-w-7xl ">
      {/* Back Button */}
      <div className="mb-6">
        <Button variant="ghost" className="pl-0" onClick={() => navigate(-1)}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Home
        </Button>
      </div>

      {/* Product Details */}
      <form onSubmit={handleSubmit(handleAddToCart)}>
        <div className="grid grid-cols-1 gap-10 sm:grid-cols-5">
          {/* Product Image */}
          <div className="sm:col-span-2">
            <div className="aspect-square overflow-hidden rounded-lg bg-gray-100">
              {product?.fullImage ? (
                <img
                  alt={product?.imageName}
                  src={product?.fullImage}
                  className="h-full w-full object-cover object-center"
                />
              ) : (
                <div className="flex items-center justify-center h-full w-full">
                  <Box className="text-gray-300 group-hover:text-gray-800 transition-colors h-1/2 w-1/2 mx-auto" />
                </div>
              )}
            </div>
          </div>

          {/* Product Info */}
          <div className="sm:col-span-3">
            <h1 className="text-3xl font-bold">{product?.description}</h1>
            {!productItem?.hideOnlinePrice && (
              <div className="mt-4">
                <span className="text-2xl font-bold">
                  {formatPrice(getEffectivePrice(product || {}))}
                </span>
              </div>
            )}
            <div className="mt-4 text-muted-foreground">
              <ReadMore text={product?.fullDesc ?? ""} maxLength={100} />
            </div>

            {/* Item Only: Recipient Info */}

            <div className="mt-6 space-y-6">
              {isOnlineGiftCard && (
                <div>
                  <label
                    htmlFor="recipientName"
                    className="block text-sm font-medium mb-1"
                  >
                    Recipient Name:
                  </label>
                  <input
                    {...register("recipientName")}
                    name="recipientName"
                    type="text"
                    className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                  />
                  {errors.recipientName && (
                    <p className="text-sm text-red-500">
                      {errors.recipientName.message}
                    </p>
                  )}
                </div>
              )}
              {isOnlineGiftCard && !isGCPhysicalTypeOnly && (
                <div>
                  <label
                    htmlFor="recipientEmail"
                    className="block text-sm font-medium mb-1"
                  >
                    Recipient Email
                  </label>
                  <input
                    {...register("recipientEmail")}
                    name="recipientEmail"
                    type="email"
                    className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                  />
                  {errors.recipientEmail && (
                    <p className="text-sm text-red-500">
                      {errors.recipientEmail.message}
                    </p>
                  )}
                </div>
              )}
              {isBothOnlineGiftCard && (
                <div>
                  <label
                    htmlFor="choosedGiftCardType"
                    className="block text-sm font-medium mb-1"
                  >
                    Gift Card Type
                  </label>
                  <div className="space-x-4">
                    <label className="inline-flex items-center">
                      <input
                        type="radio"
                        value="physical"
                        className="form-radio"
                        {...register("choosedGiftCardType")}
                      />
                      <span className="ml-2">Physical Gift Card</span>
                    </label>
                    <label className="inline-flex items-center">
                      <input
                        type="radio"
                        value="virtual"
                        className="form-radio"
                        {...register("choosedGiftCardType")}
                      />
                      <span className="ml-2">Virtual Gift Card</span>
                    </label>
                  </div>
                </div>
              )}
            </div>

            {/* Package Only: Included Items */}
            {isPackage && (
              <div className="mt-6">
                <h3 className="text-sm font-medium mb-2">Included Items</h3>
                <ul className="list-disc pl-5 text-muted-foreground text-sm">
                  {productPackage?.packageItemsPOS?.map((item) => (
                    <li key={item.itemCode}>{item.description}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Quantity */}
            <div className="mt-6">
              <h3 className="text-sm font-medium mb-2">Quantity</h3>
              <div className="flex items-center border rounded-md w-fit">
                <button
                  type="button"
                  className="px-6 py-2 hover:bg-muted"
                  onClick={decrementQuantity}
                >
                  -
                </button>
                <span className="px-6 py-2 font-medium">{quantity}</span>
                <button
                  type="button"
                  className="px-6 py-2 hover:bg-muted"
                  onClick={incrementQuantity}
                >
                  +
                </button>
              </div>
            </div>

            {/* Modifiers */}
            <div className="mt-6">
              <div className="space-y-4">
                {modifiers?.length > 0 &&
                  fields?.length === modifiers?.length &&
                  fields?.map((_, index) => {
                    switch (modifiers[index].modifierType) {
                      case ModifierType.DropDown:
                        return (
                          <div key={modifiers[index].modifierID}>
                            <ModifierDropdown
                              control={control}
                              name={`modifiers.${index}.modifierResult`}
                              options={modifiers[index].choices}
                              modifierText={modifiers[index].modifierText}
                              forceAnswer={modifiers[index].forceAnswer}
                            />
                          </div>
                        );
                      case ModifierType.CheckList:
                        return (
                          <div key={modifiers[index].modifierID}>
                            <ModifierCheckList
                              control={control}
                              name={`modifiers.${index}.choices`}
                              options={modifiers[index].choices}
                              modifierText={modifiers[index].modifierText}
                              forceAnswer={modifiers[index].forceAnswer}
                            />
                          </div>
                        );
                      case ModifierType.Text:
                        return (
                          <div key={modifiers[index].modifierID}>
                            <ModifierText
                              control={control}
                              name={`modifiers.${index}.choices`}
                              options={modifiers[index].choices}
                              modifierText={modifiers[index].modifierText}
                              forceAnswer={modifiers[index].forceAnswer}
                            />
                          </div>
                        );
                      case ModifierType.MultipleChoice:
                        return (
                          <div key={modifiers[index].modifierID}>
                            <ModifierMultipleChoice
                              control={control}
                              name={`modifiers.${index}.modifierResult`}
                              options={modifiers[index].choices}
                              modifierText={modifiers[index].modifierText}
                              forceAnswer={modifiers[index].forceAnswer}
                            />
                          </div>
                        );
                      default:
                        return null;
                    }
                  })}
              </div>
            </div>

            {isPackage && productPackage?.isContainModifiers && (
              <div className="mt-6">
                <h3 className="text-sm font-medium mb-2">Modifiers</h3>
                {productPackage?.packageItemsPOS.map((item) => {
                  return (
                    <div key={item.itemCode} className="mb-8">
                      <div className="text-sm font-semibold mb-2">
                        {item.description}
                      </div>
                      <div className="space-y-4 border-2 p-6 rounded-md">
                        {item.modifiers?.map((modifier) => {
                          // Find the globalIndex for this modifier
                          const modifierWithIndex =
                            modifiersPackageWithIndex?.find(
                              (m) => m.modifierID === modifier.modifierID
                            );
                          const globalIndex = modifierWithIndex?.globalIndex;

                          switch (modifier.modifierType) {
                            case ModifierType.DropDown:
                              return (
                                <div key={modifier.modifierID}>
                                  <ModifierDropdown
                                    control={control}
                                    name={`modifiers.${globalIndex}.modifierResult`}
                                    options={modifier.choices}
                                    modifierText={modifier.modifierText}
                                    forceAnswer={modifier.forceAnswer}
                                  />
                                </div>
                              );
                            case ModifierType.CheckList:
                              return (
                                <div key={modifier.modifierID}>
                                  <ModifierCheckList
                                    control={control}
                                    name={`modifiers.${globalIndex}.choices`}
                                    options={modifier.choices}
                                    modifierText={modifier.modifierText}
                                    forceAnswer={modifier.forceAnswer}
                                  />
                                </div>
                              );
                            case ModifierType.Text:
                              return (
                                <div key={modifier.modifierID}>
                                  <ModifierText
                                    control={control}
                                    name={`modifiers.${globalIndex}.choices`}
                                    options={modifier.choices}
                                    modifierText={modifier.modifierText}
                                    forceAnswer={modifier.forceAnswer}
                                  />
                                </div>
                              );
                            case ModifierType.MultipleChoice:
                              return (
                                <div key={modifier.modifierID}>
                                  <ModifierMultipleChoice
                                    control={control}
                                    name={`modifiers.${globalIndex}.modifierResult`}
                                    options={modifier.choices}
                                    modifierText={modifier.modifierText}
                                    forceAnswer={modifier.forceAnswer}
                                  />
                                </div>
                              );
                            default:
                              return null;
                          }
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Add to Cart Button */}
            <div className="mt-6 flex space-x-4">
              <Button size="lg" className="flex-1" type="submit">
                <ShoppingCart className="mr-2 h-5 w-5" />
                Add to Cart
              </Button>
            </div>

            {/* Shipping Info */}
            <div className="mt-8 space-y-3">
              {product?.isFreeShipping && (
                <div className="flex items-start">
                  <Truck className="h-5 w-5 mr-3 text-muted-foreground" />
                  <div>
                    <h4 className="text-sm font-medium">Free Shipping</h4>
                  </div>
                </div>
              )}

              {product?.isPickupOnly && (
                <div className="flex items-start">
                  <div className="relative inline-block w-6 h-6 mr-2">
                    <Truck className="h-5 w-5  text-muted-foreground" />
                    <X className="absolute -top-1 -left-1 w-7 h-7 text-red-600 opacity-60" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Pickup only</h4>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ProductDetailPage;
