import type { AddressFormData, WebstoreUserProfile } from "@/types";
import { baseApi } from "./baseApi";
import type { HostedTokenizeResponse } from "@/types/common";
import type {
  BillAddressViewModel,
  ShippingAddressViewModel,
  WebstoreProfileViewModel,
} from "./models";

const userProfileApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    updateBillingAddress: builder.mutation<
      void,
      {
        id: number;
        address: AddressFormData;
      }
    >({
      query: ({ id, address }) => ({
        url: `api/Webstores/Profiles/BillingAddresses/${id}`,
        method: "PUT",
        body: address,
      }),
    }),
    updateShippingAddress: builder.mutation<
      void,
      {
        id: number;
        address: AddressFormData;
      }
    >({
      query: ({ id, address }) => ({
        url: `api/Webstores/Profiles/ShippingAddresses/${id}`,
        method: "PUT",
        body: address,
      }),
    }),
    getHostedTokenizeId: builder.query<HostedTokenizeResponse, void>({
      query: () => ({
        url: `api/Webstores/GetHostedTokenizeId`,
        method: "GET",
      }),
    }),
    getUserProfile: builder.query<WebstoreUserProfile, void>({
      query: () => ({
        url: `api/Webstores/Profiles`,
        method: "GET",
      }),
    }),
    setDefaultCardOnFile: builder.mutation<
      void,
      {
        id: number;
      }
    >({
      query: ({ id }) => ({
        url: `api/Webstores/Profiles/SetDefaultCardOnFile/${id}`,
        method: "PUT",
      }),
    }),
    deleteCardOnFile: builder.mutation<
      void,
      {
        id: number;
      }
    >({
      query: ({ id }) => ({
        url: `api/Webstores/Profiles/DeleteCardOnFile/${id}`,
        method: "DELETE",
      }),
    }),
    updateUserProfile: builder.mutation<void, WebstoreProfileViewModel>({
      query: (body: WebstoreProfileViewModel) => ({
        url: `api/Webstores/Profiles`,
        method: "PUT",
        body,
      }),
    }),
    createBillingAddress: builder.mutation<number, BillAddressViewModel>({
      query: (body: BillAddressViewModel) => {
        return {
          url: `api/Webstores/Profiles/BillingAddresses`,
          method: "POST",
          body,
        };
      },
    }),
    deleteBillingAddress: builder.mutation<boolean, number>({
      query: (addressId: number) => {
        return {
          url: `api/Webstores/Profiles/BillingAddresses/${addressId}`,
          method: "DELETE",
        };
      },
    }),
    setDefaultBillingAddress: builder.mutation<void, number>({
      query: (addressId: number) => {
        return {
          url: `api/Webstores/Profiles/BillingAddresses/${addressId}/Default`,
          method: "PUT",
        };
      },
    }),
    createShippingAddress: builder.mutation<number, ShippingAddressViewModel>({
      query: (body: ShippingAddressViewModel) => {
        return {
          url: `api/Webstores/Profiles/ShippingAddresses`,
          method: "POST",
          body,
        };
      },
    }),
    deleteShippingAddress: builder.mutation<boolean, number>({
      query: (addressId: number) => {
        return {
          url: `api/Webstores/Profiles/ShippingAddresses/${addressId}`,
          method: "DELETE",
        };
      },
    }),
    setDefaultShippingAddress: builder.mutation<void, number>({
      query: (addressId: number) => {
        return {
          url: `api/Webstores/Profiles/ShippingAddresses/${addressId}/Default`,
          method: "PUT",
        };
      },
    }),
  }),
});

export const {
  useUpdateBillingAddressMutation,
  useUpdateShippingAddressMutation,
  useLazyGetHostedTokenizeIdQuery,
  useLazyGetUserProfileQuery,
  useSetDefaultCardOnFileMutation,
  useDeleteCardOnFileMutation,
  useGetHostedTokenizeIdQuery,
  useCreateBillingAddressMutation,
  useDeleteBillingAddressMutation,
  useCreateShippingAddressMutation,
  useDeleteShippingAddressMutation,
  useUpdateUserProfileMutation,
  useSetDefaultBillingAddressMutation,
  useSetDefaultShippingAddressMutation,
} = userProfileApi;
