import { Eye, EyeOff } from "lucide-react";
import { useState } from "react";
import {
  Controller,
  type Control,
  type FieldValues,
  type Path,
} from "react-hook-form";

type FieldInputProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label: string;
  type?: React.HTMLInputTypeAttribute;
  placeholder?: string;
  showEyeOn?: boolean;
  disabled?: boolean
};

const FieldInput = <T extends FieldValues>({
  name,
  control,
  label,
  type = "text",
  placeholder,
  showEyeOn = false,
  disabled = false
}: FieldInputProps<T>) => {
  const [showEye, setShowEye] = useState<boolean>(showEyeOn);
  return (
    <div className="flex flex-col gap-1">
      <label htmlFor={name}>{label}</label>
      <Controller
        name={name}
        control={control}
        disabled={disabled}
        render={({ field, fieldState }) => (
          <div className="relative">
            <input
              {...field}
              type={showEye ? type : "text"}
              placeholder={placeholder}
              className="border rounded p-2 w-full"
            />
            {showEyeOn && (
              <button
                type="button"
                tabIndex={-1}
                onClick={() => setShowEye((prev) => !prev)}
                className="absolute right-3 top-2.5 text-gray-500 hover:text-gray-700"
              >
                {showEye ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            )}
            {fieldState.error && (
              <span className="text-red-500 text-xs">
                {fieldState.error.message}
              </span>
            )}
          </div>
        )}
      />
    </div>
  );
};

export default FieldInput;
