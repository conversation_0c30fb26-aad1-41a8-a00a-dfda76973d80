import {
  Controller,
  type Control,
  type FieldValues,
  type Path,
} from "react-hook-form";
import { useState, useMemo, useCallback } from "react";
import type { Option } from "@/lib/mapToOption";

type FieldSelectorProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label: string;
  options: Option[];
  disabled?: boolean;
  placeholder?: string;
  className?: string;
};

export const FieldSelector = <T extends FieldValues>({
  name,
  control,
  label,
  options,
  disabled = false,
  placeholder = "",
  className = "",
}: FieldSelectorProps<T>): JSX.Element => {
  const [search, setSearch] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  const filteredOptions = useMemo(() => {
    return options.filter((opt) =>
      opt.label.toLowerCase().includes(search.toLowerCase())
    );
  }, [search, options]);

  const onInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearch(e.target.value);
      setIsOpen(true);
    },
    []
  );

  const onInputBlur = useCallback(
    (fieldOnChange: (val: string) => void) => {
      setTimeout(() => {
        const matchedOption = options.find(
          (opt) => opt.label.toLowerCase() === search.trim().toLowerCase()
        );
        if (matchedOption) {
          fieldOnChange(matchedOption.value);
          setSearch(matchedOption.label);
        } else {
          fieldOnChange("");
          setSearch("");
        }
        setIsOpen(false);
      }, 100);
    },
    [options, search]
  );

  const onSelect = useCallback(
    (opt: Option, fieldOnChange: (val: string) => void) => {
      const { value, label } = opt;
      fieldOnChange(value);
      setSearch(label);
      setIsOpen(false);
    },
    []
  );

  return (
    <div className={`flex flex-col gap-1 ${className}`}>
      <label htmlFor={name}>{label}</label>
      <Controller
        name={name}
        control={control}
        render={({ field, fieldState: { error } }) => {
          const currentOption = options.find((o) => o.value === field.value);

          return (
            <div className="relative">
              <input
                id={name}
                type="text"
                value={search || currentOption?.label || ""}
                onChange={onInputChange}
                onFocus={() => setIsOpen(true)}
                onBlur={() => onInputBlur(field.onChange)}
                disabled={disabled}
                placeholder={placeholder}
                className={`border p-2 w-full rounded h-10 ${
                  error ? "border-red-500" : "border-gray-300"
                }`}
              />

              {isOpen && filteredOptions.length > 0 && (
                <ul className="absolute z-10 bg-white border border-gray-300 rounded w-full max-h-48 overflow-y-auto mt-1 shadow-md">
                  {filteredOptions.map((opt) => (
                    <li
                      key={opt.value}
                      onMouseDown={() => onSelect(opt, field.onChange)}
                      className="p-2 hover:bg-gray-100 cursor-pointer"
                    >
                      {opt.label}
                    </li>
                  ))}
                </ul>
              )}
              {error && (
                <p className="text-red-500 text-xs mt-1">{error.message}</p>
              )}
            </div>
          );
        }}
      />
    </div>
  );
};
