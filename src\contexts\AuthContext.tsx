import type { WebstoreUser } from "@/types";
import { createContext } from "react";

export interface AuthContextProps {
  user: WebstoreUser | null | undefined;
  isAuthenticated: boolean;
  login: (data: {
    email: string;
    password: string;
    recaptchaToken?: string;
  }) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  authError: string | null;
}

export const AuthContext = createContext<AuthContextProps | undefined>(
  undefined
);
