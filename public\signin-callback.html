<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Authentification callback processing...</title>
    <script src="oidc-client.min.js"></script>
  </head>

  <body>
    <noscript> You need to enable JavaScript to run this app. </noscript>

    <h1>Authentification callback processing...</h1>

    <script>
      (async () => {
        try {
          const userManager = new Oidc.UserManager({ monitorSession: false });
          await userManager.signinRedirectCallback();
          window.location = "/";
        } catch (e) {
          console.error("Signin callback error:", e);
        }
      })();
    </script>
  </body>
</html>
