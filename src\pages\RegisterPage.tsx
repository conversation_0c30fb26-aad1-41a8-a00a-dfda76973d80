import { <PERSON>, useNavigate } from "react-router-dom";
import { z } from "zod";
import { useForm, useWatch, type SubmitHandler } from "react-hook-form";
import { useCallback, useEffect, useMemo, useRef } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import WelcomeBanner from "@/components/authen/WelcomeBanner";
import FieldDatePicker from "@/components/input/FieldDatePicker";
import useValidationSchema from "@/hooks/useValidationSchema";
import PasswordStrengthBar from "@/components/shared/PasswordStrengthBar";
import FieldInput from "@/components/input/FieldInput";
import { useOptions } from "@/hooks/useOptions";
import { useRegisterMutation } from "@/api/authApi";
import { useValidatePhoneNumberMutation } from "@/api/userApi";
import FieldPhoneCountry from "@/components/input/FieldPhoneCountry";
import { parsePhoneNumber } from "react-phone-number-input";
import { getErrorMessage } from "@/lib/reactQueryUtils";
import { phoneRegex, zipCodeRegex } from "@/lib/regexPattern";
import type { WebstoreMemberViewModel } from "@/api/models";
import CustomButton from "@/components/common/CustomButton";
import useRecaptchaV3 from "@/hooks/useRecaptchaV3";
import { useAuth } from "@/hooks/useAuth";

const RegisterPage = () => {
  const { options } = useOptions();
  const navigate = useNavigate();
  const [register, { isLoading: isRegisterLoading, error: registerError }] =
    useRegisterMutation();
  const { login, isLoading: isLoadingLogin, authError: loginError } = useAuth();
  const [
    validatePhoneNumber,
    { isLoading: isValidatePhoneLoading, error: validatePhoneError },
  ] = useValidatePhoneNumberMutation();

  const isLoading = useMemo<boolean>(
    () => isRegisterLoading || isLoadingLogin || isValidatePhoneLoading,
    [isLoadingLogin, isRegisterLoading, isValidatePhoneLoading]
  );
  const registerErrorRef = useRef<HTMLParagraphElement | null>(null);
  const validatePhoneErrorRef = useRef<HTMLParagraphElement | null>(null);
  const loginErrorRef = useRef<HTMLParagraphElement | null>(null);
  const dataEntryValidation = options?.webstoreOptions?.dataEntryValidation;
  const recaptcha = options?.webstoreOptions.recaptcha;

  const { passwordSchema, confirmPasswordSchema, validateConfirmMatch } =
    useValidationSchema();

  const { isReady, isAvailable, getRecaptchaToken } = useRecaptchaV3({
    enable: recaptcha?.captchaOnBoarding ?? false,
    recaptchaSiteKey: recaptcha?.captchaSiteKey ?? "",
  });

  const form = useForm<WebstoreMemberViewModel>({
    mode: "onChange",
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
      first: "",
      last: "",
      street: "",
      city: "",
      state: "",
      zip: "",
      birthDate: undefined,
      mobilePhone: undefined,
    },
    resolver: zodResolver(
      z
        .object({
          first: dataEntryValidation?.isRequiredFirstName
            ? z.string().min(1, "First name is required")
            : z.string().optional(),
          last: dataEntryValidation?.isRequiredLastName
            ? z.string().min(1, "Last name is required")
            : z.string().optional(),
          street: dataEntryValidation?.isRequiredStreet
            ? z.string().min(1, "Street is required")
            : z.string().optional(),
          city: dataEntryValidation?.isRequiredCity
            ? z.string().min(1, "City is required")
            : z.string().optional(),
          state: dataEntryValidation?.isRequiredState
            ? z.string().min(1, "State is required")
            : z.string().optional(),
          zip: dataEntryValidation?.isRequiredZip
            ? z
                .string()
                .regex(zipCodeRegex, "Invalid Zip code")
                .min(1, "Postal Code is required")
            : z
                .string()
                .optional()
                .refine((val) => !val || zipCodeRegex.test(val), {
                  message: "Invalid Zip code",
                }),
          birthDate: dataEntryValidation?.isRequiredBirthDate
            ? z
                .date({ required_error: "Birth Date is required" })
                .refine((d) => !isNaN(d.getTime()), {
                  message: "Birth Date is required",
                })
            : z.date().optional(),
          password: passwordSchema,
          confirmPassword: confirmPasswordSchema,
          email: z.string().min(1, "Email is required").email(),
          mobilePhone: dataEntryValidation?.isRequiredPhone
            ? z
                .string()
                .regex(phoneRegex, "Invalid phone number")
                .min(1, "Phone is requied")
            : z
                .string()
                .optional()
                .refine((val) => !val || phoneRegex.test(val), {
                  message: "Invalid phone number",
                }),
        })
        .superRefine((values, ctx) => {
          validateConfirmMatch(values.password, values.confirmPassword, ctx);
        })
    ),
  });

  const { control, handleSubmit, reset } = form;
  const [password] = useWatch({
    control,
    name: ["password"],
  });

  const onSubmit = useCallback<SubmitHandler<WebstoreMemberViewModel>>(
    async (formData: WebstoreMemberViewModel) => {
      try {
        const recaptchaToken = await getRecaptchaToken("register");

        // validate phone
        if (formData.mobilePhone) {
          const phone = parsePhoneNumber(formData.mobilePhone);
          await validatePhoneNumber({
            phoneNumber: phone?.nationalNumber ?? "",
            countryCode: phone?.country ?? "",
          }).unwrap();
        }

        // register
        const registerResult = await register({
          ...formData,
          recaptchaToken,
        }).unwrap();

        // auto login
        if (registerResult.isSuccess)
          await login({
            email: formData.email,
            password: formData.password,
            recaptchaToken: await getRecaptchaToken("login"),
          })
            .then(() => {
              navigate("/");
              reset();
            })
            .catch((err) => {
              console.error("Login failed", err);
            });
      } catch (err) {
        console.error("Registration failed", err);
      }
    },
    [getRecaptchaToken, register, login, reset, navigate, validatePhoneNumber]
  );

  // auto scroll to focus error
  useEffect(() => {
    if (validatePhoneError && validatePhoneErrorRef.current) {
      window.scrollTo({
        top: validatePhoneErrorRef.current.offsetTop - 150,
      });
    } else if (registerError && registerErrorRef.current) {
      window.scrollTo({
        top: registerErrorRef.current.offsetTop - 150,
      });
    }
  }, [registerError, validatePhoneError]);

  let recaptchaMessage = "";
  if (!isReady) {
    recaptchaMessage = "reCAPTCHA not ready yet";
  } else if (!isAvailable) {
    recaptchaMessage = "reCAPTCHA not available";
  }

  const recaptchaContent = (
    <p className="text-red-500 text-xs">{recaptchaMessage}</p>
  );

  return (
    <main className="flex flex-1 min-h-[600px] max-w-7xl mx-auto">
      <aside className="flex justify-center items-center bg-gray-100 w-[600px] max-md:w-6/12 max-sm:hidden">
        <WelcomeBanner />
      </aside>
      <section className="flex flex-1 justify-center pt-24 pb-12">
        <div className="flex flex-col items-center w-[399px]">
          <h5 className="mb-1 text-sm text-muted-foreground font-medium w-3/4">
            Welcome!
          </h5>
          <h2 className="mb-6 text-2xl font-bold w-3/4">Create an Account</h2>

          {(recaptcha?.captchaOnBoarding ?? false) && recaptchaContent}

          <form className="w-3/4" onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-4">
              <div>
                <FieldInput<WebstoreMemberViewModel>
                  name="email"
                  control={control}
                  label="Email"
                  type="email"
                />
              </div>
              {!dataEntryValidation?.isHideFirstName && (
                <div>
                  <FieldInput<WebstoreMemberViewModel>
                    name="first"
                    control={control}
                    label="First Name"
                    type="text"
                  />
                </div>
              )}
              {!dataEntryValidation?.isHideLastName && (
                <div>
                  <FieldInput<WebstoreMemberViewModel>
                    name="last"
                    control={control}
                    label="Last Name"
                    type="text"
                  />
                </div>
              )}
              <div>
                <FieldInput<WebstoreMemberViewModel>
                  name="password"
                  control={control}
                  label="Password"
                  type="password"
                  showEyeOn
                />
                <PasswordStrengthBar password={password} />
              </div>
              <div>
                <FieldInput<WebstoreMemberViewModel>
                  name="confirmPassword"
                  control={control}
                  label="Confirm Password"
                  type="password"
                  showEyeOn
                />
              </div>
              {!dataEntryValidation?.isHidePhone && (
                <div>
                  <FieldPhoneCountry<WebstoreMemberViewModel>
                    name="mobilePhone"
                    control={control}
                    label="Mobile Phone Number"
                    type="tel"
                  />
                </div>
              )}
              {validatePhoneError && (
                <p className="text-red-500 text-xs" ref={validatePhoneErrorRef}>
                  {getErrorMessage(validatePhoneError)}
                </p>
              )}
              {!dataEntryValidation?.isHideBirthDate && (
                <div>
                  <FieldDatePicker<WebstoreMemberViewModel>
                    name="birthDate"
                    control={control}
                    label="Birth Date"
                    maxDate={new Date()}
                  />
                </div>
              )}
              {!dataEntryValidation?.isHideStreet && (
                <div>
                  <FieldInput<WebstoreMemberViewModel>
                    name="street"
                    control={control}
                    label="Address"
                    type="text"
                  />
                </div>
              )}
              {!dataEntryValidation?.isHideCity && (
                <div>
                  <FieldInput<WebstoreMemberViewModel>
                    name="city"
                    control={control}
                    label="City"
                    type="text"
                  />
                </div>
              )}
              {!dataEntryValidation?.isHideState && (
                <div>
                  <FieldInput<WebstoreMemberViewModel>
                    name="state"
                    control={control}
                    label="State"
                    type="text"
                  />
                </div>
              )}
              {!dataEntryValidation?.isHideZip && (
                <div>
                  <FieldInput<WebstoreMemberViewModel>
                    name="zip"
                    control={control}
                    label="Postal Code"
                    type="text"
                  />
                </div>
              )}
            </div>
            <CustomButton variant="default" type="submit" isLoading={isLoading}>
              Create Account
            </CustomButton>
            <Link to="/login">
              <Button
                variant="outline"
                className="mt-2 w-full"
                disabled={isLoading}
              >
                Back
              </Button>

              {registerError && (
                <p className="text-red-500 text-xs" ref={registerErrorRef}>
                  {getErrorMessage(registerError)}
                </p>
              )}

              {loginError && (
                <p className="text-red-500 text-xs" ref={loginErrorRef}>
                  {getErrorMessage(loginError)}
                </p>
              )}
            </Link>
            <div className="flex justify-center items-center mt-4">
              <span className="text-sm text-muted-foreground">
                Already have an account?
              </span>
              <Link to="/login">
                <Button
                  size="icon"
                  variant="link"
                  className="ml-1 text-blue-600"
                  disabled={isLoading}
                >
                  Login
                </Button>
              </Link>
            </div>
          </form>
        </div>
      </section>
    </main>
  );
};

export default RegisterPage;
