import logo from "../../assets/logo.png";
import { useOptions } from "@/hooks/useOptions";

const WelcomeBanner = () => {
  const { options } = useOptions();
  const logoImageSource =
    options?.webstoreOptions?.welcomeMessage?.welcomeScreenBannerImage;
  const logoImage = logoImageSource ? (
    <img
      src={`data:image/png;base64,${logoImageSource}`}
      alt="Club Prophet Logo"
    />
  ) : (
    <img src={logo} alt="Club Prophet Logo" className="h-auto w-80" />
  );

  return <div>{logoImage}</div>;
};

export default WelcomeBanner;
