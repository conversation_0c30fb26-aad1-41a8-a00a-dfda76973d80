import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import {
  CircleUser,
  LogIn,
  LogOut,
  MapPinHouse,
  Menu,
  PackageOpen,
  ShoppingCart,
  Trash2,
  User,
  X,
} from "lucide-react";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "../ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useOptions } from "@/hooks/useOptions";
import type { Department } from "@/api/optionApi";
import { useAuth } from "@/hooks/useAuth";
import { useCart } from "@/hooks/useCart";
import type { CartItem } from "@/api/productApi";
import { formatPrice } from "@/lib/utils";
import { AuthService } from "@/auth/AuthService";

const CartIcon = ({
  totalItems,
  totalPrice,
  cart,
  removeFromCart,
  isEmpty,
}: {
  totalItems: number;
  totalPrice: number;
  cart: CartItem[];
  removeFromCart: (itemCode: string) => void;
  isEmpty: boolean;
}) => (
  <Sheet>
    <SheetTrigger asChild>
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="icon" className="relative">
          <ShoppingCart className="w-5 h-5" />
          {totalItems > 0 && (
            <span className="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 text-xs font-medium text-black bg-secondary rounded-full">
              {totalItems}
            </span>
          )}
        </Button>
        <div className="hidden sm:flex flex-col text-xs">
          <div>Cart</div>
          <div>{formatPrice(totalPrice)}</div>
        </div>
      </div>
    </SheetTrigger>
    <SheetContent className="flex flex-col">
      <SheetHeader>
        <SheetTitle>Cart Summary</SheetTitle>
      </SheetHeader>
      <div className="flex-1 overflow-auto my-4 flex gap-2 flex-col">
        {cart?.map((item) => (
          <div
            key={item.itemCode}
            className="flex items-center space-x-2 text-sm border rounded-md p-4"
          >
            <div className="flex-1">{item.description}</div>
            <div>{formatPrice(item.quantity * (item.price ?? 0))}</div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => removeFromCart(item.itemCode)}
              className="text-destructive border-destructive hover:bg-destructive/90 hover:text-destructive-foreground"
            >
              <Trash2 className="h-3.5 w-3.5" />
            </Button>
          </div>
        ))}
        {isEmpty && (
          <div className="py-4 text-center text-lg text-muted-foreground">
            Your cart is currently empty.
          </div>
        )}
      </div>
      <div className="flex flex-col space-y-4">
        {!isEmpty && (
          <SheetClose asChild>
            <Link to="/checkout">
              <Button className="w-full">Proceed to checkout</Button>
            </Link>
          </SheetClose>
        )}
        <SheetClose asChild>
          <Link to="/cart">
            <Button variant="outline" className="w-full">
              Your Cart
            </Button>
          </Link>
        </SheetClose>
      </div>
    </SheetContent>
  </Sheet>
);

const UserMenu = ({ handleLogout }: { handleLogout: () => void }) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="ghost">
        <CircleUser className="w-5 h-5" />
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent className="w-52">
      <DropdownMenuLabel>My Account</DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        <Link to="/profile">
          <DropdownMenuItem className="py-2">
            <User /> Profile
          </DropdownMenuItem>
        </Link>
        <Link to="/orders">
          <DropdownMenuItem className="py-2">
            <PackageOpen /> Orders
          </DropdownMenuItem>
        </Link>
        <Link to="/address">
          <DropdownMenuItem className="py-2">
            <MapPinHouse /> Addresses
          </DropdownMenuItem>
        </Link>
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuItem className="py-2" onClick={handleLogout}>
        <LogOut /> Log out
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
);

const MobileMenu = ({
  open,
  onClose,
  departments,
}: {
  open: boolean;
  onClose: () => void;
  departments: Department[];
}) => (
  <div
    className={`fixed inset-0 z-[60] bg-background transition-transform duration-300 ease-in-out text-black ${
      open ? "translate-x-0" : "-translate-x-full"
    }`}
  >
    <div className="flex items-center justify-between p-4 border-b">
      <span className="text-xl font-bold">FASHION</span>
      <Button variant="ghost" size="icon" onClick={onClose}>
        <X className="w-5 h-5" />
      </Button>
    </div>

    <Accordion type="single" collapsible className="w-full">
      {departments.map((dep: Department) => (
        <AccordionItem key={dep.id} value={`item-${dep.id}`} className="px-4">
          <AccordionTrigger
            className={(dep.children?.length ?? 0) <= 0 ? "[&>svg]:hidden" : ""}
          >
            {dep.children?.length <= 0 ? (
              <Link
                to={{
                  pathname: "/",
                  search: `?department=${dep.name}`,
                }}
                onClick={onClose}
                className="block py-2 pl-4 text-start w-full hover:bg-gray-100"
              >
                {dep.name}
              </Link>
            ) : (
              <span className="flex items-center justify-between w-full py-2 pl-4">
                {dep.name}
              </span>
            )}
          </AccordionTrigger>
          {dep.children?.map((child: Department) => (
            <AccordionContent key={child.id} className="pl-2">
              <Link
                to={{
                  pathname: "/",
                  search: `?department=${child.name}`,
                }}
                onClick={onClose}
                className="block py-2 pl-4 hover:bg-gray-100"
              >
                {child.name}
              </Link>
            </AccordionContent>
          ))}
        </AccordionItem>
      ))}
    </Accordion>
  </div>
);

const Header = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { options } = useOptions();
const { isAuthenticated, logout } = useAuth();
  const { getTotalItems, cart, removeFromCart, getTotalPrice, isEmpty } =
    useCart();

  useEffect(() => {
    const handleResize = () => setMobileMenuOpen(false);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const departments = options?.departments || [];
  const mainDepartments = departments
    .filter((dep) => !dep.parentName)
    .sort((a, b) => (a.order ?? 0) - (b.order ?? 0));

  const handleLogout = () => {
    logout();
    const authService = new AuthService();
    authService.logout();
  };

  const handleLogin = () => {
    const authService = new AuthService();
    authService.login();
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-primary text-white">
      <div className="w-full">
        <div className="mx-auto max-w-7xl flex items-center justify-between h-16 px-4 md:h-20">
          {/* Mobile menu toggle */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setMobileMenuOpen(true)}
          >
            <Menu className="w-5 h-5" />
          </Button>

          {/* Logo */}
          <Link to="/" className="text-xl font-bold">
            {options?.webstoreOptions?.adminCourseDetail?.storeName}
          </Link>

          {/* Right Section */}
          <div className="flex items-center space-x-2">
            <CartIcon
              totalItems={getTotalItems()}
              totalPrice={getTotalPrice()}
              cart={cart}
              isEmpty={isEmpty}
              removeFromCart={removeFromCart}
            />
            {isAuthenticated ? (
              <UserMenu handleLogout={handleLogout} />
            ) : (
              <Button variant="ghost" size="icon" onClick={handleLogin}>
                <LogIn className="w-5 h-5" />
              </Button>
            )}
          </div>

          {/* Mobile menu */}
          <MobileMenu
            open={mobileMenuOpen}
            onClose={() => setMobileMenuOpen(false)}
            departments={mainDepartments}
          />
        </div>
      </div>
    </header>
  );
};

export default Header;
