import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Controller, type Control, type FieldValues, type Path, } from 'react-hook-form';

type FieldDatePickerProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label: string;
  placeholder?: string;
  maxDate?: Date;
};

const FieldDatePicker = <T extends FieldValues>({
  name,
  control,
  label,
  placeholder = 'Pick a date',
  maxDate
}: FieldDatePickerProps<T>) => {
  return (
    <div className="flex flex-col gap-1">
      <label className="block text-sm font-medium mb-1" htmlFor={name}>
        {label}
      </label>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  'w-full justify-start text-left font-normal',
                  !field.value && 'text-muted-foreground'
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {field.value ? format(field.value, 'PPP') : <span>{placeholder}</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={field.value ? new Date(field.value) : undefined}
                onSelect={field.onChange}
                initialFocus
                toDate={maxDate}
              />
            </PopoverContent>
          </Popover>
        )}
      />
    </div>
  );
};

export default FieldDatePicker;
