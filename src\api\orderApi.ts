import type { OrderDetail } from "@/types";
import { baseApi } from "./baseApi";
import type { ApiResponse } from "./productApi";
import type { OrderDetailViewModel } from "./models/orderDetailViewModel";
import type { PaginationFilter } from "./models/paginationFilter";
import type { OrderHistoryPaginationViewModel } from "./models/paginationViewModel";
import type { WebStoreApiResponse } from "./models/webStoreApiResponse";

const orderApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getOrder: builder.query<OrderDetail, string>({
      query: (orderCode) => ({
        url: `api/Webstores/Orders/${orderCode}`,
        method: "GET",
      }),
      transformResponse: (response: ApiResponse<OrderDetail>) => {
        return response.data;
      },
    }),
    placeOrder: builder.mutation<ApiResponse<OrderDetail>, string>({
      query: (recaptchaToken) => ({
        url: `api/Webstores/Orders/Place`,
        method: "POST",
        body: { recaptchaToken }
      }),
    }),
    getOrderHistory: builder.query<
      OrderHistoryPaginationViewModel,
      PaginationFilter
    >({
      query: ({ pageSize, currentPage }: PaginationFilter) => ({
        url: `api/Webstores/OrderHistory`,
        method: "GET",
        params: { pageSize, currentPage },
      }),
      providesTags: ["Order"],
    }),
    
    getOrderDetail: builder.query<WebStoreApiResponse<OrderDetailViewModel>, number>({
      query: (orderId: number) => ({
        url: `api/Webstores/Orders/${orderId}`,
        method: 'GET'
      })
    })
  }),
});

export const { useGetOrderQuery, usePlaceOrderMutation, useGetOrderHistoryQuery, useGetOrderDetailQuery  } = orderApi;
