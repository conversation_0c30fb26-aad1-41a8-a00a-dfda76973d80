import { UserManager } from "oidc-react";

export const oidcConfig = {
  authority: import.meta.env.VITE_IDENTITY_API,
  client_id: import.meta.env.VITE_CLIENT_ID,
  redirect_uri: window.location.origin + "/auth-callback",
  post_logout_redirect_uri: window.location.origin,
  response_type: "id_token token",
  scope: "openid profile references",
  client_secret: import.meta.env.VITE_CLIENT_SECRET,
};

export const userManager = new UserManager(oidcConfig);
