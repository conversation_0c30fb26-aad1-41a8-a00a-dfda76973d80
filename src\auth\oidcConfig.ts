import { UserManager, WebStorageStateStore } from "oidc-client-ts";

export const oidcConfig = {
  authority: import.meta.env.VITE_IDENTITY_API,
  client_id: import.meta.env.VITE_CLIENT_ID,
  redirect_uri: window.location.origin + (import.meta.env.VITE_BASE_PATH || "/") + "auth-callback",
  post_logout_redirect_uri: window.location.origin + (import.meta.env.VITE_BASE_PATH || "/"),
  response_type: "code",
  scope: "openid profile references",
  client_secret: import.meta.env.VITE_CLIENT_SECRET,
  userStore: new WebStorageStateStore({ store: window.localStorage }),
  monitorSession: false,
};

export const userManager = new UserManager(oidcConfig);
