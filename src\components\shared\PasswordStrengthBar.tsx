import { useMemo } from "react";
import { Progress } from "../ui/progress";
import {
  alphabetLowerRegex,
  alphabetUpperRegex,
  minPasswordLength,
  numericRegex,
  specialCharRegex,
} from "@/constants/constant";

const passwordStrengthMessages: Record<0 | 20 | 40 | 60 | 80 | 100, string> = {
  0: "Very Weak",
  20: "Weak",
  40: "Medium",
  60: "Strong",
  80: "Very Strong",
  100: "Excellent",
};

type PasswordStrengthBarProps = {
  password: string;
};

const PasswordStrengthBar = ({ password }: PasswordStrengthBarProps) => {
  const passwordStrength = useMemo<0 | 20 | 40 | 60 | 80 | 100>(() => {
    let strength = 0;

    if (password.length >= minPasswordLength) strength += 20;
    if (alphabetLowerRegex.test(password)) strength += 20;
    if (alphabetUpperRegex.test(password)) strength += 20;
    if (numericRegex.test(password)) strength += 20;
    if (specialCharRegex.test(password)) strength += 20;

    return strength as 0 | 20 | 40 | 60 | 80 | 100;
  }, [password]);

  return (
    <div className="flex items-center mt-2 justify-between">
      <Progress
        value={passwordStrength}
        className="h-1 [&>div]:bg-green-500 rounded-full w-full"
      />
      <span className="text-xs text-muted-foreground ml-2 min-w-20 flex-grow text-right">
        {passwordStrengthMessages[passwordStrength]}
      </span>
    </div>
  );
};

export default PasswordStrengthBar;
