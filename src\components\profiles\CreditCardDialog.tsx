import {
  <PERSON><PERSON>,
  <PERSON><PERSON>C<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Tit<PERSON>,
} from "@/components/ui/dialog";
import { SafeIframe } from "../common/SafeIframe";
import { useLazyGetHostedTokenizeIdQuery } from "@/api/userProfileApi";
import { useEffect, useState } from "react";
import type { HostedTokenizeResponse } from "@/types/common";
import { useUserProfile } from "@/hooks/useUserProfile";
import { toast } from "@/hooks/use-toast";
import { Button } from "../ui/button";

interface CreditCardDialogProps {
  isOpen: boolean;
  onClose: () => void;
  setIsOpen: (isOpen: boolean) => void;
}

const CreditCardDialog = ({
  isOpen,
  onClose,
  setIsOpen,
}: CreditCardDialogProps) => {
  const [hostedTokenizeUrl, setHostedTokenizeUrl] = useState<string | null>(
    null
  );
  const [allowOrigin, setAllowO<PERSON><PERSON>] = useState<string | null>(null);
  const [isShowCloseButton, setIsShowCloseButton] = useState(false);

  const [fetchHostedTokenizeId, { isLoading }] =
    useLazyGetHostedTokenizeIdQuery();

  const { getUserProfile } = useUserProfile();

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (
        event.origin === allowOrigin &&
        event.data?.type === "resSuccess" &&
        event.data?.value === "true"
      ) {
        setIsOpen(false);
        getUserProfile();
        toast({
          title: "✅ Your payment method has been added.",
          variant: "default",
        });
      }
    };

    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, [allowOrigin, getUserProfile, setIsOpen]);

  useEffect(() => {
    if (!isOpen) return;

    fetchHostedTokenizeId()
      .unwrap()
      .then((res: HostedTokenizeResponse) => {
        if (res.success && res.hostedTokenizeUrl) {
          setHostedTokenizeUrl(res.hostedTokenizeUrl);

          const redirectUrl = new URL(res.hostedTokenizeUrl).searchParams.get(
            "RedirectUrl"
          );
          if (redirectUrl) {
            setAllowOrigin(new URL(redirectUrl).origin);
          }
        } else {
          setIsShowCloseButton(true);
        }
      });
  }, [isOpen, fetchHostedTokenizeId]);

  const renderSkeletonContent = () => {
    if (isLoading) {
      return (
        <div className="flex flex-col space-y-4 p-4 animate-pulse">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-12 bg-gray-200 rounded-md" />
          ))}
        </div>
      );
    }

    return <SafeIframe url={hostedTokenizeUrl} />;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-lg"
        aria-description="Add Payment"
        onInteractOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>Add New Payment</DialogTitle>
        </DialogHeader>
        <div className="w-full h-[735px]">{renderSkeletonContent()}</div>
        <input type="hidden" name="Success" id="Success" value="false" />
        {isShowCloseButton && (
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
            </DialogClose>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CreditCardDialog;
