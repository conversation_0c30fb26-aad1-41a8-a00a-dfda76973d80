import {
  useGetProductsQuery,
  useLazyGetProductDetailsQuery,
  type WebstoreProduct,
} from "@/api/productApi";
import { useOptions } from "@/hooks/useOptions";
import {
  type ReactNode,
  useState,
  useMemo,
  useEffect,
  useCallback,
} from "react";
import { ProductsContext } from "./ProductsContext";

export const ProductsProvider = ({ children }: { children: ReactNode }) => {
  const { data, isLoading, isSuccess } = useGetProductsQuery(undefined); // Load all
  const [triggerGetProductDetails, { isLoading: isLoadingDetails }] =
    useLazyGetProductDetailsQuery(undefined);
  const [allProducts, setAllProducts] = useState<WebstoreProduct[]>([]);
  const { options } = useOptions();
  const departments = useMemo(() => options?.departments || [], [options]);

  useEffect(() => {
    if (isSuccess && data) {
      setAllProducts(data);
    }
  }, [isSuccess, data]);

  const getFilteredProducts = useCallback(
    (department: string | null) => {
      if (!department) return allProducts;

      const departmentId = departments.find(
        (d) => d.name.toString() === department
      )?.id;

      if (!departmentId) {
        for (const dept of departments) {
          const childDept = dept.children?.find(
            (child) => child.name.toString() === department
          );
          if (childDept) {
            return allProducts.filter(
              (product) => product.departmentId === childDept.id
            );
          }
        }
      }

      return allProducts.filter(
        (product) => product.departmentId === departmentId
      );
    },
    [allProducts, departments]
  );

  const getSelectedProduct = useCallback(
    async (itemCode: string | null) => {
      if (!itemCode) return null;
      const result = await triggerGetProductDetails(itemCode).unwrap();
      return result;
    },
    [triggerGetProductDetails]
  );

  const value = useMemo(
    () => ({
      allProducts,
      loading: isLoading || isLoadingDetails,
      getFilteredProducts,
      getSelectedProduct,
    }),
    [
      allProducts,
      isLoading,
      isLoadingDetails,
      getFilteredProducts,
      getSelectedProduct,
    ]
  );

  return (
    <ProductsContext.Provider value={value}>
      {children}
    </ProductsContext.Provider>
  );
};
