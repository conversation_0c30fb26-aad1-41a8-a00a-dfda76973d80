import { MapPin } from "lucide-react";

const NoDefaultAddress = ({ form }: { form: "billing" | "shipping" }) => {
  const label = form === "billing" ? "Billing" : "Shipping";

  return (
    <div className="text-center text-muted-foreground">
      <MapPin className="h-12 w-12 mx-auto mb-4" aria-hidden="true" />
      <h3 className="text-lg font-semibold text-foreground mb-2">
        No default {label} address
      </h3>
      <p className="mb-4">
        Add your first {label} address. It will be set as your default automatically.
      </p>
    </div>
  );
};

export default NoDefaultAddress;