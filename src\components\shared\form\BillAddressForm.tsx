import { useForm, useWatch, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { zipCodeRegex } from "@/lib/regexPattern";
import FieldInput from "@/components/input/FieldInput";
import { CheckboxField } from "@/components/input/CheckbokField";
import { Button } from "@/components/ui/button";
import { FieldSelector } from "@/components/input/FieldSelector";
import { countries } from "@/data/countryRegions";
import { useCallback, useEffect, useMemo } from "react";
import { mapToOption, type Option } from "@/lib/mapToOption";
import type { BillAddressViewModel } from "@/api/models";
import { toast } from "@/hooks/use-toast";
import { useUserProfile } from "@/hooks/useUserProfile";

const BillAddressForm = ({
  id,
  onCancel = () => {},
  disableSetIsDefault = false,
}: {
  id?: number;
  onCancel?: () => void;
  disableSetIsDefault?: boolean;
}) => {
  const { userProfile, updateBillingAddress, addBillingAddress, loading } =
    useUserProfile();
  const existingBillAddress = useMemo(
    () =>
      userProfile?.userAddress.billingAddresses.find(
        (b) => b.webstoreBillingAddressId === id
      ),
    [id, userProfile?.userAddress.billingAddresses]
  );

  const form = useForm<BillAddressViewModel>({
    defaultValues: {
      webstoreBillingAddressId: 0,
      firstName: "",
      lastName: "",
      street: "",
      city: "",
      zip: "",
      state: "",
      country: "",
      isDefault: true,
    },
    resolver: zodResolver(
      z.object({
        firstName: z.string().min(1, "First name is required."),
        lastName: z.string().min(1, "Last name is required."),
        street: z.string().min(1, "Street is required."),
        city: z.string().min(1, "City is required."),
        state: z.string().min(1, "State is required."),
        zip: z
          .string()
          .min(1, "Zip is required.")
          .refine((val) => !val || zipCodeRegex.test(val)),
        country: z.string().min(1, "Country is required."),
        isDefault: z.boolean().optional(),
        webstoreBillingAddressId: z.number().optional(),
      })
    ),
  });

  const { control, handleSubmit, reset } = form;

  const selectedCountry = useWatch({ control, name: "country" });

  const countriesOption = useMemo<Option[]>(
    () =>
      mapToOption(
        countries,
        "countryName",
        (i) => `${i.countryName} (${i.countryShortCode})`
      ),
    []
  );
  const stateOption = useMemo<Option[]>(
    () =>
      selectedCountry
        ? mapToOption(
            countries.find((c) => c.countryName === selectedCountry)!.regions,
            "name",
            "name"
          )
        : mapToOption(
            countries.flatMap((i) => i.regions),
            "name",
            "name"
          ),
    [selectedCountry]
  );

  const onSubmit = useCallback<SubmitHandler<BillAddressViewModel>>(
    async (formData) => {
      try {
        const request = {
          ...formData,
          email: userProfile?.email ?? "",
          mobilePhone: userProfile?.mobile_phone ?? "",
        };
        if (!id) {
          await addBillingAddress(request);
        } else {
          await updateBillingAddress(request.webstoreBillingAddressId, request);
        }

        toast({ title: "✅ Update Profile bill address successfully" });
        onCancel();
      } catch (err) {
        console.error("Unable to update bill Address", err);
        toast({
          title: "❌ Unable to update bill Address",
        });
      }
    },
    [
      addBillingAddress,
      id,
      onCancel,
      updateBillingAddress,
      userProfile?.email,
      userProfile?.mobile_phone,
    ]
  );

  useEffect(() => {
    if (!existingBillAddress) return;

    reset({ ...existingBillAddress });
  }, [existingBillAddress, form, id, reset]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 py-4">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <FieldInput<BillAddressViewModel>
            name="firstName"
            control={control}
            label="First name"
            type="text"
          />
        </div>
        <div>
          <FieldInput<BillAddressViewModel>
            name="lastName"
            control={control}
            label="Last name"
            type="text"
          />
        </div>
      </div>
      <div>
        <FieldSelector<BillAddressViewModel>
          name="country"
          control={control}
          label="Country"
          options={countriesOption}
        />
      </div>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <FieldSelector<BillAddressViewModel>
            name="state"
            control={control}
            label="State / Province"
            options={stateOption}
          />
        </div>
        <div>
          <FieldInput<BillAddressViewModel>
            name="city"
            control={control}
            label="City"
            type="text"
          />
        </div>
      </div>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <FieldInput<BillAddressViewModel>
            name="zip"
            control={control}
            label="ZIP / Postal code"
          />
        </div>
        <div>
          <FieldInput<BillAddressViewModel>
            name="street"
            control={control}
            label="Street address"
            type="text"
          />
        </div>
      </div>
      <div className="pt-2">
        <CheckboxField<BillAddressViewModel>
          name="isDefault"
          control={control}
          label="Set as default address"
          disabled={disableSetIsDefault}
        />
      </div>
      <div className="flex justify-end space-x-2 mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={() => {
            onCancel();
          }}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          Update Address
        </Button>
      </div>
    </form>
  );
};
export default BillAddressForm;
