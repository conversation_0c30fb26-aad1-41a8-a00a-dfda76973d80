import { saveLocalStorage } from "@/lib/utils";
import { baseApi } from "./baseApi";
import { localStorageWebsiteIdKey } from "@/constants/constant";

export interface WebstoreOptionsResponse {
  webstoreOptions: WebstoreOptions;
  banners: WebStoreBanner[];
  departments: Department[];
  webSiteId: string;
}

export interface Department {
  description: string;
  id: number;
  parentName: string;
  name: string;
  order: number;
  parentId: number;
  children: Department[];
}

export interface WebstoreOptions {
  adminCourseDetail: AdminCourseDetail;
  creditCardSetting: CreditCardSetting;
  dataEntryValidation: DataEntryValidation;
  policySetting: PolicySetting;
  recaptcha: Recaptcha;
  shippingSetting: ShippingSetting;
  siteConfiguration: SiteConfiguration;
  themeEditor: ThemeEditor;
  webStoreBannerSetting: WebStoreBannerSetting;
  welcomeMessage: WelcomeMessage;
}

export interface AdminCourseDetail {
  storeName: string;
  storePhone: string;
  storeEmail: string;
  storeWebsite: string;
  storeAddress: string;
}

export interface CreditCardSetting {
  isUseCreditCardOnFile: boolean;
  useCCMv4: boolean;
  ccMv4_Host: string;
  ccMv4_User: string;
  ccMv4_Token: string;
  ccMv4_Profile: string;
}

export interface DataEntryValidation {
  // Required
  isRequiredFirstName: boolean;
  isRequiredLastName: boolean;
  isRequiredPhone: boolean;
  isRequiredBirthDate: boolean;
  isRequiredUsername: boolean;
  isRequiredStreet: boolean;
  isRequiredCity: boolean;
  isRequiredState: boolean;
  isRequiredZip: boolean;

  // Check Duplicated
  // isCheckDuplicateFirstName: boolean;
  // isCheckDuplicateLastName: boolean;
  // isCheckDuplicatePhone: boolean;
  // isCheckDuplicateBirthDate: boolean;
  // isCheckDuplicateUsername: boolean;
  // isCheckDuplicateStreet: boolean;
  // isCheckDuplicateCity: boolean;
  // isCheckDuplicateState: boolean;
  // isCheckDuplicateZip: boolean;

  // Editable
  isEditableFirstName: boolean;
  isEditableLastName: boolean;
  isEditablePhone: boolean;
  isEditableBirthDate: boolean;
  isEditableUsername: boolean;
  isEditableStreet: boolean;
  isEditableCity: boolean;
  isEditableState: boolean;
  isEditableZip: boolean;

  // Hide
  isHideFirstName: boolean;
  isHideLastName: boolean;
  isHidePhone: boolean;
  isHideBirthDate: boolean;
  isHideUsername: boolean;
  isHideStreet: boolean;
  isHideCity: boolean;
  isHideState: boolean;
  isHideZip: boolean;

  assignNewAccountToStore: number;
  enableAssignNewAccountToStore: boolean;
}

export interface PolicySetting {
  enableTermOfService: boolean;
  termOfServiceLinkType: string;
  termOfServiceHyperlink: string;
  termOfServiceContent: string | TrustedHTML;

  enablePrivacyPolicy: boolean;
  privacyPolicyLinkType: string;
  privacyPolicyHyperlink: string;
  privacyPolicyContent: string | TrustedHTML;

  enableRefundPolicy: boolean;
  refundPolicyLinkType: string;
  refundPolicyHyperlink: string;
  refundPolicyContent: string | TrustedHTML;

  enableCancellationPolicy: boolean;
  cancellationPolicyLinkType: string;
  cancellationPolicyHyperlink: string;
  cancellationPolicyContent: string | TrustedHTML;
}

export interface Recaptcha {
  captchaSiteKey: string;
  captchaOnBoarding: boolean;
  captchaCheckoutProcess: boolean;
}

export interface WelcomeMessage {
  welcomeScreenBannerImage: string;
}

export interface ShippingSetting {
  shippingStandardDesc?: string;
  shippingExpressDesc?: string;
  shippingOvernightDesc?: string;
  shippingPickupDesc?: string;
  shippingStandardPrice?: number;
  shippingExpressPrice?: number;
  shippingOvernightPrice?: number;
  shippingPickupPrice?: number;
  enableShippingExpress?: boolean;
  enableShippingStandard?: boolean;
  enableShippingOvernight?: boolean;
  enableShippingPickup?: boolean;
  defaultShippingMethod?: number;
}

export interface SiteConfiguration {
  timeDisplay?: number;
  dateFormat?: number;
  currencyCode?: string;
  defaultSiteLanguage?: string;
}

export interface ThemeEditor {
  primaryColor?: string;
  secondaryColor?: string;
  bannerFontStyle?: string;
}

export interface WebStoreBannerSetting {
  enableMainPageBanner?: boolean;
}

export interface WebStoreBanner {
  webStoreBannerId: number;
  linkTypeDescription: string;
  departmentName: string;
  websiteId: string;
  image: string;
  customLink: string;
  linkType: number;
}

export const optionApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getWebstoreOptions: builder.query<WebstoreOptionsResponse, string>({
      query: (siteName) => ({
        url: `api/Webstores/GetAllOptions/${siteName}`,
        method: "GET",
      }),
      providesTags: ["Option"],
      transformResponse: (response: WebstoreOptionsResponse) => {
        if (response?.webSiteId) {
          saveLocalStorage<string>(
            localStorageWebsiteIdKey,
            response.webSiteId
          );
        }

        const nestedDepartments = buildNestedDepartments(response.departments);
        return {
          ...response,
          departments: nestedDepartments,
        };
      },
    }),
  }),
});

const buildNestedDepartments = (flatList: Department[]): Department[] => {
  const idMap = new Map<number, Department>();
  const rootItems: Department[] = [];

  flatList.forEach((item) => {
    idMap.set(item.id, { ...item, children: [] });
  });

  flatList.forEach((item) => {
    if (item.parentId != null) {
      const parent = idMap.get(item.parentId);
      if (parent) {
        parent.children.push(idMap.get(item.id)!);
      }
    } else {
      rootItems.push(idMap.get(item.id)!);
    }
  });

  const sortItems = (items: Department[]) => {
    items.sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
    items.forEach((item) => {
      if (item.children) sortItems(item.children);
    });
  };

  sortItems(rootItems);
  return rootItems;
};

export const { useGetWebstoreOptionsQuery } = optionApi;
