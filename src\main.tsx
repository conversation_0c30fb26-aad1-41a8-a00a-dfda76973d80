import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.tsx";
import { Provider } from "react-redux";
import { persistor, store } from "./store.ts";
import { ProductsProvider } from "./contexts/ProductsProvider.tsx";
import { CartProvider } from "./contexts/CartProvider.tsx";
import { AuthProvider } from "./contexts/AuthProvider.tsx";
import { BrowserRouter } from "react-router-dom";
import { UserProfileProvider } from "./contexts/UserProfileProvider.tsx";
import { applyTheme, prefetchOption } from "./lib/prefetch.ts";
import OptionsProvider from "./contexts/OptionsProvider.tsx";
import { PersistGate } from "redux-persist/integration/react";
import { AlertDialogProvider } from "./contexts/AlertDialogProvider.tsx";

async function prepareBeforeRenderDOM() {
  const optionResult = await prefetchOption(import.meta.env.VITE_SITE_NAME);

  if (optionResult?.webstoreOptions?.themeEditor)
    await applyTheme(optionResult.webstoreOptions.themeEditor);

  return { optionResult };
}

async function initDom() {
  const { optionResult } = await prepareBeforeRenderDOM();
  createRoot(document.getElementById("root")!).render(
    <StrictMode>
      <BrowserRouter basename={import.meta.env.VITE_BASE_PATH}>
        <Provider store={store}>
          <PersistGate loading={null} persistor={persistor}>
            {optionResult && (
              <OptionsProvider option={optionResult}>
                <AlertDialogProvider>
                  <CartProvider>
                    <AuthProvider>
                      <UserProfileProvider>
                        <ProductsProvider>
                          <App />
                        </ProductsProvider>
                      </UserProfileProvider>
                    </AuthProvider>
                  </CartProvider>
                </AlertDialogProvider>
              </OptionsProvider>
            )}
          </PersistGate>
        </Provider>
      </BrowserRouter>
    </StrictMode>
  );
}

initDom();
