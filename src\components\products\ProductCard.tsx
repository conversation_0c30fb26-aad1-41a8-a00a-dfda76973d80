import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import type { WebstoreProduct } from "@/api/productApi";
import { Box } from "lucide-react";
import { formatPrice, getEffectivePrice } from "@/lib/utils";

interface ProductCardProps {
  product: WebstoreProduct;
}

const ProductCard = ({ product }: ProductCardProps) => {
  const price = getEffectivePrice(product);

  return (
    <div className="group relative">
      <Link to={`/product/${product.itemCode}`}>
        <div className="aspect-[3/4] overflow-hidden rounded-lg bg-gray-100 h-56 w-full">
          {product.thumbnailImage ? (
            <img
              alt={product.imageName}
              src={product.thumbnailImage}
              className="h-full w-full object-fill object-center transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            <div className="flex items-center justify-center h-full w-full">
              <Box className="text-gray-300 group-hover:text-gray-800 transition-colors h-1/2 w-1/2 mx-auto" />
            </div>
          )}
        </div>
        <div className="mt-3 flex justify-between items-center">
          <div>
            <h3 className="tetx-sm font-medium">
              <Link to={`/product/${product.itemCode}`}>
                {product.description}
              </Link>
            </h3>
          </div>
          {product && !product.hideOnlinePrice && (
            <p className="text-md font-medium">{formatPrice(price)}</p>
          )}
        </div>
        <div className="mt-2 invisible group-hover:visible transition-opacity">
          <Button variant="outline" className="w-full rounded-md">
            View Product
          </Button>
        </div>
      </Link>
    </div>
  );
};

export default ProductCard;
