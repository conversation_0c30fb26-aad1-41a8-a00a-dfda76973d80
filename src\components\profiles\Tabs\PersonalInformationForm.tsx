import { useForm, type SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useEffect, useCallback } from "react";

import FieldInput from "@/components/input/FieldInput";
import FieldPhoneCountry from "@/components/input/FieldPhoneCountry";
import { Button } from "@/components/ui/button";
import { phoneRegex } from "@/lib/regexPattern";
import { useUserProfile } from "@/hooks/useUserProfile";
import { toast } from "@/hooks/use-toast";
import type { UserProfile } from "@/types";

type PersonalInfoFormType = Pick<
  UserProfile,
  "first_name" | "last_name" | "email" | "mobile_phone"
>;

const PersonalInformationForm = ({ onCancel }: { onCancel: () => void }) => {
  const { userProfile, updateUserProfile, loading } = useUserProfile();

  const form = useForm<PersonalInfoFormType>({
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      mobile_phone: "",
    },
    resolver: zodResolver(
      z.object({
        first_name: z.string().min(1, "First name is required"),
        last_name: z.string().min(1, "Last name is required"),
        email: z
          .string()
          .min(1, "Email is required")
          .email("Invalid email format"),
        mobile_phone: z
          .string()
          .optional()
          .refine((val) => !val || phoneRegex.test(val), {
            message: "Invalid phone number",
          }),
      })
    ),
  });

  const { control, handleSubmit, reset } = form;

  const onSubmit = useCallback<SubmitHandler<PersonalInfoFormType>>(
    async (formData) => {
      try {
        await updateUserProfile({
          first: formData.first_name,
          last: formData.last_name,
          email: formData.email,
          mobilePhone: formData.mobile_phone,
        });
        toast({ title: "✅ Profile updated successfully" });
        onCancel();
      } catch (err) {
        console.error("Unable to update profile", err);
        toast({ title: "❌ Unable to update profile" });
      }
    },
    [updateUserProfile, onCancel]
  );

  useEffect(() => {
    if (!userProfile) return;

    reset({
      first_name: userProfile.first_name ?? "",
      last_name: userProfile.last_name ?? "",
      email: userProfile.email ?? "",
      mobile_phone: userProfile.mobile_phone ?? "",
    });
  }, [userProfile, reset]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 py-4">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <FieldInput<PersonalInfoFormType>
          name="first_name"
          control={control}
          label="First name"
          type="text"
        />
        <FieldInput<PersonalInfoFormType>
          name="last_name"
          control={control}
          label="Last name"
          type="text"
        />
      </div>

      <FieldInput<PersonalInfoFormType>
        name="email"
        control={control}
        label="Email"
        type="email"
      />

      <FieldPhoneCountry<PersonalInfoFormType>
        name="mobile_phone"
        control={control}
        label="Mobile phone"
        type="tel"
      />

      <div className="flex justify-end space-x-2 mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          Save Changes
        </Button>
      </div>
    </form>
  );
};

export default PersonalInformationForm;
